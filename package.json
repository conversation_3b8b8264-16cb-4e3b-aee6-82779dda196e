{"name": "wha-assist", "version": "1.0.0", "description": "WhatsApp-Telegram AI Assistant with RAG capabilities", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["whatsapp", "telegram", "ai", "assistant", "rag", "chatbot"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"whatsapp-web.js": "^1.23.0", "node-telegram-bot-api": "^0.64.0", "openai": "^4.20.1", "@pinecone-database/pinecone": "^1.1.2", "@langchain/community": "^0.0.25", "@langchain/openai": "^0.0.14", "pg": "^8.11.3", "redis": "^4.6.10", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bull": "^4.12.2", "node-cron": "^3.0.3", "qrcode-terminal": "^0.12.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "moment": "^2.29.4", "axios": "^1.6.2", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "@types/node": "^20.10.4", "babel-jest": "^29.7.0", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6"}, "engines": {"node": ">=18.0.0"}}