const databaseConfig = require('../../config/database');
const logger = require('../../utils/logger');

class DatabaseService {
    constructor() {
        this.pool = null;
    }

    async initialize() {
        this.pool = await databaseConfig.initializePostgreSQL();
    }

    // Message operations
    async saveMessage(messageData) {
        const query = `
            INSERT INTO messages (
                id, from_number, to_number, body, timestamp, is_group, 
                contact_name, chat_id, message_type, media_url, media_mimetype,
                has_media, is_forwarded, reply_to_id
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            ON CONFLICT (id) DO UPDATE SET
                body = EXCLUDED.body,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                messageData.id,
                messageData.from,
                messageData.to,
                messageData.body,
                messageData.timestamp,
                messageData.isGroup || false,
                messageData.contact?.name,
                messageData.chat?.id,
                messageData.type || 'text',
                messageData.mediaUrl,
                messageData.mediaMimetype,
                messageData.hasMedia || false,
                messageData.isForwarded || false,
                messageData.replyToId
            ]);
            
            logger.info('Message saved successfully', { messageId: messageData.id });
            return result.rows[0];
        } catch (error) {
            logger.error('Error saving message:', error);
            throw error;
        }
    }

    async getTodayMessages(contactNumber) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const query = `
            SELECT * FROM messages 
            WHERE (from_number = $1 OR to_number = $1)
            AND timestamp >= $2
            ORDER BY timestamp ASC
            LIMIT 100
        `;
        
        try {
            const result = await this.pool.query(query, [
                contactNumber,
                today.getTime()
            ]);
            
            return result.rows;
        } catch (error) {
            logger.error('Error fetching today messages:', error);
            throw error;
        }
    }

    async getMessageHistory(contactNumber, limit = 50, offset = 0) {
        const query = `
            SELECT * FROM messages 
            WHERE (from_number = $1 OR to_number = $1)
            ORDER BY timestamp DESC
            LIMIT $2 OFFSET $3
        `;
        
        try {
            const result = await this.pool.query(query, [contactNumber, limit, offset]);
            return result.rows;
        } catch (error) {
            logger.error('Error fetching message history:', error);
            throw error;
        }
    }

    // Contact operations
    async saveContact(contactData) {
        const query = `
            INSERT INTO contacts (
                number, name, profile_pic_url, is_business, 
                is_enterprise, labels, last_seen, is_blocked
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            ON CONFLICT (number) DO UPDATE SET
                name = EXCLUDED.name,
                profile_pic_url = EXCLUDED.profile_pic_url,
                is_business = EXCLUDED.is_business,
                is_enterprise = EXCLUDED.is_enterprise,
                labels = EXCLUDED.labels,
                last_seen = EXCLUDED.last_seen,
                is_blocked = EXCLUDED.is_blocked,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                contactData.number,
                contactData.name,
                contactData.profilePicUrl,
                contactData.isBusiness || false,
                contactData.isEnterprise || false,
                contactData.labels || [],
                contactData.lastSeen,
                contactData.isBlocked || false
            ]);
            
            return result.rows[0];
        } catch (error) {
            logger.error('Error saving contact:', error);
            throw error;
        }
    }

    async getContact(number) {
        const query = 'SELECT * FROM contacts WHERE number = $1';
        
        try {
            const result = await this.pool.query(query, [number]);
            return result.rows[0];
        } catch (error) {
            logger.error('Error fetching contact:', error);
            throw error;
        }
    }

    // Scheduled message operations
    async saveScheduledMessage(toNumber, message, scheduledFor, createdBy = null) {
        const query = `
            INSERT INTO scheduled_messages (to_number, message, scheduled_for, created_by)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                toNumber,
                message,
                scheduledFor,
                createdBy
            ]);
            
            logger.info('Scheduled message saved', { id: result.rows[0].id });
            return result.rows[0];
        } catch (error) {
            logger.error('Error saving scheduled message:', error);
            throw error;
        }
    }

    async getPendingScheduledMessages() {
        const query = `
            SELECT * FROM scheduled_messages 
            WHERE status = 'pending' 
            AND scheduled_for <= CURRENT_TIMESTAMP
            AND attempts < max_attempts
            ORDER BY scheduled_for ASC
        `;
        
        try {
            const result = await this.pool.query(query);
            return result.rows;
        } catch (error) {
            logger.error('Error fetching pending scheduled messages:', error);
            throw error;
        }
    }

    async updateScheduledMessageStatus(id, status, errorMessage = null, executedAt = null) {
        const query = `
            UPDATE scheduled_messages 
            SET status = $2, error_message = $3, executed_at = $4, 
                attempts = attempts + 1, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                id,
                status,
                errorMessage,
                executedAt || new Date()
            ]);
            
            return result.rows[0];
        } catch (error) {
            logger.error('Error updating scheduled message status:', error);
            throw error;
        }
    }

    // AI conversation operations
    async saveAIConversation(userId, platform, context) {
        const query = `
            INSERT INTO ai_conversations (user_id, platform, conversation_context, total_messages)
            VALUES ($1, $2, $3, 1)
            ON CONFLICT (user_id) DO UPDATE SET
                conversation_context = EXCLUDED.conversation_context,
                last_interaction = CURRENT_TIMESTAMP,
                total_messages = ai_conversations.total_messages + 1,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                userId,
                platform,
                JSON.stringify(context)
            ]);
            
            return result.rows[0];
        } catch (error) {
            logger.error('Error saving AI conversation:', error);
            throw error;
        }
    }

    async getAIConversation(userId) {
        const query = 'SELECT * FROM ai_conversations WHERE user_id = $1';
        
        try {
            const result = await this.pool.query(query, [userId]);
            return result.rows[0];
        } catch (error) {
            logger.error('Error fetching AI conversation:', error);
            throw error;
        }
    }

    // Message embedding operations
    async saveMessageEmbedding(messageId, vectorId, embeddingModel = 'text-embedding-ada-002') {
        const query = `
            INSERT INTO message_embeddings (message_id, vector_id, embedding_model)
            VALUES ($1, $2, $3)
            ON CONFLICT (message_id) DO NOTHING
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [messageId, vectorId, embeddingModel]);
            return result.rows[0];
        } catch (error) {
            logger.error('Error saving message embedding:', error);
            throw error;
        }
    }

    // System logging
    async logSystem(level, message, metadata = {}, service = null) {
        const query = `
            INSERT INTO system_logs (level, message, metadata, service)
            VALUES ($1, $2, $3, $4)
            RETURNING *
        `;
        
        try {
            const result = await this.pool.query(query, [
                level,
                message,
                JSON.stringify(metadata),
                service
            ]);
            
            return result.rows[0];
        } catch (error) {
            console.error('Error logging to database:', error);
            // Don't throw here to avoid infinite loops
        }
    }

    // Cleanup operations
    async cleanupOldMessages(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        
        const query = `
            DELETE FROM messages 
            WHERE created_at < $1
            RETURNING COUNT(*)
        `;
        
        try {
            const result = await this.pool.query(query, [cutoffDate]);
            logger.info(`Cleaned up old messages`, { deletedCount: result.rowCount });
            return result.rowCount;
        } catch (error) {
            logger.error('Error cleaning up old messages:', error);
            throw error;
        }
    }

    // Health check
    async healthCheck() {
        try {
            const result = await this.pool.query('SELECT NOW()');
            return { status: 'healthy', timestamp: result.rows[0].now };
        } catch (error) {
            logger.error('Database health check failed:', error);
            return { status: 'unhealthy', error: error.message };
        }
    }
}

module.exports = DatabaseService;
