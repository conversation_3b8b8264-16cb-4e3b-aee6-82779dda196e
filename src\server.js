const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const logger = require('./utils/logger');
const { handleValidationError } = require('./middleware/validation');

class Server {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3000;
        this.apiPrefix = process.env.API_PREFIX || '/api/v1';
        
        // Service references
        this.whatsappClient = null;
        this.telegramBot = null;
        this.aiAssistant = null;
        this.dbService = null;
        this.vectorService = null;
        this.contextService = null;
        this.schedulerService = null;
        
        this.setupMiddleware();
        this.setupHealthCheck();
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                }
            }
        }));

        // CORS configuration
        this.app.use(cors({
            origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
            allowedHeaders: ['Content-Type', 'Authorization', 'x-api-key'],
            credentials: true
        }));

        // Rate limiting
        const limiter = rateLimit({
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
            max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
            message: {
                error: 'Too many requests from this IP, please try again later.',
                retryAfter: Math.ceil((parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000) / 1000)
            },
            standardHeaders: true,
            legacyHeaders: false,
            skip: (req) => {
                // Skip rate limiting for health checks
                return req.path === '/health' || req.path === `${this.apiPrefix}/health`;
            }
        });
        this.app.use(limiter);

        // HTTP request logging
        this.app.use(morgan('combined', { stream: logger.stream }));

        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Request ID middleware
        this.app.use((req, res, next) => {
            req.id = require('uuid').v4();
            res.setHeader('X-Request-ID', req.id);
            next();
        });

        // Request logging
        this.app.use((req, res, next) => {
            logger.api('Incoming request', {
                requestId: req.id,
                method: req.method,
                url: req.url,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
            next();
        });
    }

    setupHealthCheck() {
        // Basic health check
        this.app.get('/health', async (req, res) => {
            try {
                const health = {
                    status: 'healthy',
                    timestamp: new Date().toISOString(),
                    uptime: process.uptime(),
                    version: process.env.npm_package_version || '1.0.0',
                    environment: process.env.NODE_ENV || 'development',
                    services: {}
                };

                // Check WhatsApp service
                if (this.whatsappClient) {
                    const whatsappStatus = this.whatsappClient.getStatus();
                    health.services.whatsapp = {
                        status: whatsappStatus.isReady ? 'healthy' : 'unhealthy',
                        ready: whatsappStatus.isReady,
                        qrGenerated: whatsappStatus.qrCodeGenerated
                    };
                }

                // Check database service
                if (this.dbService) {
                    const dbHealth = await this.dbService.healthCheck();
                    health.services.database = dbHealth;
                }

                // Check AI service
                if (this.aiAssistant) {
                    const aiHealth = await this.aiAssistant.healthCheck();
                    health.services.ai = aiHealth;
                }

                // Check vector service
                if (this.vectorService) {
                    const vectorHealth = await this.vectorService.healthCheck();
                    health.services.vector = vectorHealth;
                }

                // Check context service
                if (this.contextService) {
                    const contextHealth = await this.contextService.healthCheck();
                    health.services.context = contextHealth;
                }

                // Check scheduler service
                if (this.schedulerService) {
                    const schedulerHealth = await this.schedulerService.healthCheck();
                    health.services.scheduler = schedulerHealth;
                }

                // Determine overall health
                const unhealthyServices = Object.values(health.services)
                    .filter(service => service.status === 'unhealthy');
                
                if (unhealthyServices.length > 0) {
                    health.status = 'degraded';
                    res.status(503);
                }

                res.json(health);

            } catch (error) {
                logger.error('Health check failed:', error);
                res.status(503).json({
                    status: 'unhealthy',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Detailed health check
        this.app.get(`${this.apiPrefix}/health/detailed`, async (req, res) => {
            try {
                const detailed = {
                    status: 'healthy',
                    timestamp: new Date().toISOString(),
                    system: {
                        uptime: process.uptime(),
                        memory: process.memoryUsage(),
                        cpu: process.cpuUsage(),
                        version: process.version,
                        platform: process.platform,
                        pid: process.pid
                    },
                    services: {}
                };

                // Add detailed service checks here
                // ... (similar to basic health check but with more details)

                res.json(detailed);

            } catch (error) {
                logger.error('Detailed health check failed:', error);
                res.status(503).json({
                    status: 'unhealthy',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    setupRoutes() {
        // Import route classes
        const WhatsAppRoutes = require('./routes/whatsapp');
        const AIRoutes = require('./routes/ai');

        // Setup route instances
        const whatsappRoutes = new WhatsAppRoutes(this.whatsappClient, this.dbService);
        const aiRoutes = new AIRoutes(this.aiAssistant, this.vectorService, this.contextService);

        // Mount routes
        this.app.use(`${this.apiPrefix}/whatsapp`, whatsappRoutes.getRouter());
        this.app.use(`${this.apiPrefix}/ai`, aiRoutes.getRouter());

        // Root endpoint
        this.app.get('/', (req, res) => {
            res.json({
                name: 'WhatsApp AI Assistant',
                version: process.env.npm_package_version || '1.0.0',
                status: 'running',
                timestamp: new Date().toISOString(),
                endpoints: {
                    health: '/health',
                    whatsapp: `${this.apiPrefix}/whatsapp`,
                    ai: `${this.apiPrefix}/ai`
                }
            });
        });

        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Endpoint not found',
                path: req.originalUrl,
                method: req.method,
                timestamp: new Date().toISOString()
            });
        });
    }

    setupErrorHandling() {
        // Validation error handler
        this.app.use(handleValidationError);

        // Global error handler
        this.app.use((error, req, res, next) => {
            logger.error('Unhandled error:', {
                error: error.message,
                stack: error.stack,
                requestId: req.id,
                url: req.url,
                method: req.method
            });

            // Don't leak error details in production
            const isDevelopment = process.env.NODE_ENV === 'development';
            
            res.status(error.status || 500).json({
                error: isDevelopment ? error.message : 'Internal server error',
                requestId: req.id,
                timestamp: new Date().toISOString(),
                ...(isDevelopment && { stack: error.stack })
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception:', error);
            this.gracefulShutdown('uncaughtException');
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
            this.gracefulShutdown('unhandledRejection');
        });

        // Handle SIGTERM
        process.on('SIGTERM', () => {
            logger.info('SIGTERM received');
            this.gracefulShutdown('SIGTERM');
        });

        // Handle SIGINT
        process.on('SIGINT', () => {
            logger.info('SIGINT received');
            this.gracefulShutdown('SIGINT');
        });
    }

    // Set service references
    setServices(services) {
        this.whatsappClient = services.whatsappClient;
        this.telegramBot = services.telegramBot;
        this.aiAssistant = services.aiAssistant;
        this.dbService = services.dbService;
        this.vectorService = services.vectorService;
        this.contextService = services.contextService;
        this.schedulerService = services.schedulerService;
    }

    async start() {
        try {
            // Setup routes after services are set
            this.setupRoutes();
            this.setupErrorHandling();

            // Start server
            this.server = this.app.listen(this.port, () => {
                logger.info(`Server started successfully`, {
                    port: this.port,
                    environment: process.env.NODE_ENV || 'development',
                    apiPrefix: this.apiPrefix,
                    pid: process.pid
                });
                
                console.log(`🚀 Server running on port ${this.port}`);
                console.log(`📡 API available at http://localhost:${this.port}${this.apiPrefix}`);
                console.log(`❤️  Health check at http://localhost:${this.port}/health`);
            });

            // Handle server errors
            this.server.on('error', (error) => {
                logger.error('Server error:', error);
            });

        } catch (error) {
            logger.error('Failed to start server:', error);
            throw error;
        }
    }

    async gracefulShutdown(signal) {
        logger.info(`Graceful shutdown initiated by ${signal}`);

        try {
            // Stop accepting new requests
            if (this.server) {
                this.server.close(() => {
                    logger.info('HTTP server closed');
                });
            }

            // Close services
            if (this.schedulerService) {
                await this.schedulerService.stopScheduler();
            }

            if (this.whatsappClient) {
                await this.whatsappClient.destroy();
            }

            if (this.telegramBot) {
                await this.telegramBot.stop();
            }

            // Close database connections
            if (this.dbService) {
                const databaseConfig = require('./config/database');
                await databaseConfig.closeConnections();
            }

            logger.info('Graceful shutdown completed');
            process.exit(0);

        } catch (error) {
            logger.error('Error during graceful shutdown:', error);
            process.exit(1);
        }
    }
}

module.exports = Server;
