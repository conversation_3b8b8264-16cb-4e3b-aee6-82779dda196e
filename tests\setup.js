// Jest setup file
require('dotenv').config({ path: '.env.test' });

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';
process.env.OPENAI_API_KEY = 'test-openai-key';
process.env.PINECONE_API_KEY = 'test-pinecone-key';
process.env.TELEGRAM_BOT_TOKEN = 'test-telegram-token';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.JWT_SECRET = 'test-jwt-secret';

// Global test utilities
global.testUtils = {
  // Create mock message data
  createMockMessage: (overrides = {}) => ({
    id: 'msg_123',
    from: '+1234567890',
    to: '+0987654321',
    body: 'Test message',
    timestamp: Date.now(),
    isGroup: false,
    isFromMe: false,
    type: 'text',
    contact: {
      name: 'Test User',
      number: '+1234567890',
      isBusiness: false
    },
    chat: {
      id: 'chat_123',
      name: 'Test Chat',
      isGroup: false
    },
    ...overrides
  }),

  // Create mock user data
  createMockUser: (overrides = {}) => ({
    id: 'user_123',
    platform: 'whatsapp',
    name: 'Test User',
    ...overrides
  }),

  // Create mock AI response
  createMockAIResponse: (overrides = {}) => ({
    text: 'Test AI response',
    functionCalls: [],
    replyToMessage: false,
    ...overrides
  }),

  // Wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock database query result
  createMockQueryResult: (rows = [], overrides = {}) => ({
    rows,
    rowCount: rows.length,
    command: 'SELECT',
    ...overrides
  })
};

// Mock external dependencies
jest.mock('whatsapp-web.js', () => ({
  Client: jest.fn().mockImplementation(() => ({
    initialize: jest.fn(),
    on: jest.fn(),
    sendMessage: jest.fn(),
    getChats: jest.fn(),
    getContacts: jest.fn(),
    destroy: jest.fn()
  })),
  LocalAuth: jest.fn(),
  MessageMedia: {
    fromFilePath: jest.fn()
  }
}));

jest.mock('node-telegram-bot-api', () => {
  return jest.fn().mockImplementation(() => ({
    setMyCommands: jest.fn(),
    onText: jest.fn(),
    on: jest.fn(),
    sendMessage: jest.fn(),
    answerCallbackQuery: jest.fn(),
    stopPolling: jest.fn()
  }));
});

jest.mock('qrcode-terminal', () => ({
  generate: jest.fn()
}));

// Suppress console output during tests unless explicitly needed
const originalConsole = { ...console };
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// Global test hooks
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Clean up after each test
  jest.restoreAllMocks();
});

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
