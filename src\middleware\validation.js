const Joi = require('joi');
const logger = require('../utils/logger');

// Validation middleware factory
const validate = (schema, property = 'body') => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req[property], {
            abortEarly: false,
            stripUnknown: true
        });

        if (error) {
            const errorDetails = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));

            logger.warn('Validation error', { 
                errors: errorDetails,
                endpoint: req.path 
            });

            return res.status(400).json({
                error: 'Validation failed',
                details: errorDetails
            });
        }

        // Replace the original data with validated data
        req[property] = value;
        next();
    };
};

// Common validation schemas
const schemas = {
    // Phone number validation
    phoneNumber: Joi.string()
        .pattern(/^\+?[1-9]\d{1,14}$/)
        .required()
        .messages({
            'string.pattern.base': 'Phone number must be in international format'
        }),

    // Message validation
    message: Joi.string()
        .min(1)
        .max(4096)
        .required()
        .messages({
            'string.min': 'Message cannot be empty',
            'string.max': 'Message too long (max 4096 characters)'
        }),

    // Date validation
    datetime: Joi.date()
        .iso()
        .min('now')
        .required()
        .messages({
            'date.min': 'Date must be in the future'
        }),

    // Pagination
    pagination: Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20),
        offset: Joi.number().integer().min(0).default(0)
    }),

    // Search query
    searchQuery: Joi.string()
        .min(2)
        .max(200)
        .required()
        .messages({
            'string.min': 'Search query must be at least 2 characters',
            'string.max': 'Search query too long'
        })
};

// Specific endpoint validations
const validations = {
    // Send message validation
    sendMessage: validate(Joi.object({
        to: schemas.phoneNumber,
        message: schemas.message
    })),

    // Schedule message validation
    scheduleMessage: validate(Joi.object({
        to: schemas.phoneNumber,
        message: schemas.message,
        scheduledFor: schemas.datetime
    })),

    // Search messages validation
    searchMessages: validate(Joi.object({
        query: schemas.searchQuery,
        contact: schemas.phoneNumber.optional(),
        limit: Joi.number().integer().min(1).max(50).default(10),
        minScore: Joi.number().min(0).max(1).default(0.7)
    })),

    // Get message history validation
    getMessageHistory: validate(Joi.object({
        contact: schemas.phoneNumber,
        limit: Joi.number().integer().min(1).max(100).default(50),
        offset: Joi.number().integer().min(0).default(0)
    })),

    // AI chat validation
    aiChat: validate(Joi.object({
        message: schemas.message,
        userId: Joi.string().required(),
        platform: Joi.string().valid('whatsapp', 'telegram').default('whatsapp')
    })),

    // Update scheduled message validation
    updateScheduledMessage: validate(Joi.object({
        status: Joi.string().valid('pending', 'completed', 'failed', 'cancelled').optional(),
        scheduledFor: schemas.datetime.optional()
    })),

    // Query parameters validation
    queryParams: {
        pagination: validate(schemas.pagination, 'query'),
        
        contact: validate(Joi.object({
            contact: schemas.phoneNumber
        }), 'query'),

        dateRange: validate(Joi.object({
            startDate: Joi.date().iso().required(),
            endDate: Joi.date().iso().min(Joi.ref('startDate')).required()
        }), 'query'),

        status: validate(Joi.object({
            status: Joi.string().valid('pending', 'completed', 'failed', 'cancelled').optional()
        }), 'query')
    }
};

// Custom validation functions
const customValidations = {
    // Validate WhatsApp number format
    validateWhatsAppNumber: (phoneNumber) => {
        // Remove any non-digit characters except +
        const cleaned = phoneNumber.replace(/[^\d+]/g, '');
        
        // Check if it's a valid international format
        const isValid = /^\+?[1-9]\d{1,14}$/.test(cleaned);
        
        if (!isValid) {
            throw new Error('Invalid WhatsApp number format');
        }
        
        // Ensure it starts with + for WhatsApp
        return cleaned.startsWith('+') ? cleaned : `+${cleaned}`;
    },

    // Validate cron expression
    validateCronExpression: (cronExpr) => {
        const cronRegex = /^(\*|([0-5]?\d)) (\*|([01]?\d|2[0-3])) (\*|([0-2]?\d|3[01])) (\*|([0-9]|1[0-2])) (\*|([0-6]))$/;
        
        if (!cronRegex.test(cronExpr)) {
            throw new Error('Invalid cron expression');
        }
        
        return cronExpr;
    },

    // Validate file upload
    validateFileUpload: (file) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];
        const maxSize = 10 * 1024 * 1024; // 10MB
        
        if (!allowedTypes.includes(file.mimetype)) {
            throw new Error('File type not allowed');
        }
        
        if (file.size > maxSize) {
            throw new Error('File too large (max 10MB)');
        }
        
        return true;
    }
};

// Error handling middleware for validation
const handleValidationError = (error, req, res, next) => {
    if (error.name === 'ValidationError') {
        return res.status(400).json({
            error: 'Validation failed',
            message: error.message,
            details: error.details
        });
    }
    next(error);
};

module.exports = {
    validate,
    schemas,
    validations,
    customValidations,
    handleValidationError
};
