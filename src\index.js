require('dotenv').config();

const logger = require('./utils/logger');
const Server = require('./server');

// Import services
const WhatsAppClient = require('./services/whatsapp/client');
const TelegramBotService = require('./services/telegram/bot');
const AIAssistant = require('./services/ai/assistant');
const DatabaseService = require('./services/database/dbService');
const VectorService = require('./services/database/vectorService');
const ContextService = require('./services/ai/contextService');
const SchedulerService = require('./services/scheduler/schedulerService');

class WhatsAppAssistant {
    constructor() {
        this.services = {};
        this.server = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            logger.info('🚀 Starting WhatsApp AI Assistant...');
            
            // Validate environment variables
            this.validateEnvironment();

            // Initialize services in order
            await this.initializeServices();
            
            // Connect services
            this.connectServices();
            
            // Start server
            await this.startServer();
            
            this.isInitialized = true;
            
            logger.info('✅ WhatsApp AI Assistant started successfully!');
            this.logSystemInfo();
            
        } catch (error) {
            logger.error('❌ Failed to initialize WhatsApp Assistant:', error);
            await this.cleanup();
            process.exit(1);
        }
    }

    validateEnvironment() {
        const requiredEnvVars = [
            'OPENAI_API_KEY',
            'PINECONE_API_KEY',
            'TELEGRAM_BOT_TOKEN',
            'DATABASE_URL',
            'REDIS_URL'
        ];

        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        
        if (missingVars.length > 0) {
            throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
        }

        logger.info('Environment variables validated');
    }

    async initializeServices() {
        try {
            // Initialize database service first
            logger.info('Initializing database service...');
            this.services.dbService = new DatabaseService();
            await this.services.dbService.initialize();
            logger.info('✅ Database service initialized');

            // Initialize vector service
            logger.info('Initializing vector service...');
            this.services.vectorService = new VectorService();
            await this.services.vectorService.initialize();
            logger.info('✅ Vector service initialized');

            // Initialize context service
            logger.info('Initializing context service...');
            this.services.contextService = new ContextService();
            await this.services.contextService.initialize();
            logger.info('✅ Context service initialized');

            // Initialize AI assistant
            logger.info('Initializing AI assistant...');
            this.services.aiAssistant = new AIAssistant();
            await this.services.aiAssistant.initialize();
            logger.info('✅ AI assistant initialized');

            // Initialize WhatsApp client
            logger.info('Initializing WhatsApp client...');
            this.services.whatsappClient = new WhatsAppClient();
            // Note: WhatsApp client initialization is async and happens in background
            logger.info('✅ WhatsApp client created (initialization in progress)');

            // Initialize Telegram bot
            if (process.env.TELEGRAM_BOT_TOKEN) {
                logger.info('Initializing Telegram bot...');
                this.services.telegramBot = new TelegramBotService(process.env.TELEGRAM_BOT_TOKEN);
                await this.services.telegramBot.initialize();
                logger.info('✅ Telegram bot initialized');
            } else {
                logger.warn('Telegram bot token not provided, skipping Telegram integration');
            }

            // Initialize scheduler service
            logger.info('Initializing scheduler service...');
            this.services.schedulerService = new SchedulerService();
            await this.services.schedulerService.initialize();
            logger.info('✅ Scheduler service initialized');

        } catch (error) {
            logger.error('Error initializing services:', error);
            throw error;
        }
    }

    connectServices() {
        try {
            logger.info('Connecting services...');

            // Connect AI assistant to other services
            this.services.aiAssistant.setWhatsAppClient(this.services.whatsappClient);
            if (this.services.telegramBot) {
                this.services.aiAssistant.setTelegramBot(this.services.telegramBot);
            }

            // Connect WhatsApp client to AI assistant
            this.services.whatsappClient.setAIAssistant(this.services.aiAssistant);

            // Connect Telegram bot to AI assistant and WhatsApp client
            if (this.services.telegramBot) {
                this.services.telegramBot.setAIAssistant(this.services.aiAssistant);
                this.services.telegramBot.setWhatsAppClient(this.services.whatsappClient);
            }

            // Connect scheduler to messaging services
            this.services.schedulerService.setWhatsAppClient(this.services.whatsappClient);
            if (this.services.telegramBot) {
                this.services.schedulerService.setTelegramBot(this.services.telegramBot);
            }

            logger.info('✅ Services connected successfully');

        } catch (error) {
            logger.error('Error connecting services:', error);
            throw error;
        }
    }

    async startServer() {
        try {
            logger.info('Starting HTTP server...');
            
            this.server = new Server();
            this.server.setServices(this.services);
            await this.server.start();
            
            logger.info('✅ HTTP server started');

        } catch (error) {
            logger.error('Error starting server:', error);
            throw error;
        }
    }

    async startWhatsAppClient() {
        try {
            logger.info('Starting WhatsApp client initialization...');
            await this.services.whatsappClient.initialize();
            logger.info('✅ WhatsApp client fully initialized');
        } catch (error) {
            logger.error('Error initializing WhatsApp client:', error);
            // Don't throw here as other services can still work
        }
    }

    logSystemInfo() {
        const info = {
            version: process.env.npm_package_version || '1.0.0',
            nodeVersion: process.version,
            platform: process.platform,
            environment: process.env.NODE_ENV || 'development',
            pid: process.pid,
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            services: {
                whatsapp: !!this.services.whatsappClient,
                telegram: !!this.services.telegramBot,
                ai: !!this.services.aiAssistant,
                database: !!this.services.dbService,
                vector: !!this.services.vectorService,
                context: !!this.services.contextService,
                scheduler: !!this.services.schedulerService,
                server: !!this.server
            }
        };

        logger.info('System information:', info);

        // Log to console for visibility
        console.log('\n📊 System Status:');
        console.log(`   Version: ${info.version}`);
        console.log(`   Environment: ${info.environment}`);
        console.log(`   Node.js: ${info.nodeVersion}`);
        console.log(`   Platform: ${info.platform}`);
        console.log(`   PID: ${info.pid}`);
        console.log(`   Memory: ${Math.round(info.memory.heapUsed / 1024 / 1024)}MB`);
        console.log('\n🔧 Services:');
        Object.entries(info.services).forEach(([service, status]) => {
            console.log(`   ${service}: ${status ? '✅' : '❌'}`);
        });
        console.log('\n');
    }

    async getSystemStatus() {
        try {
            const status = {
                initialized: this.isInitialized,
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                services: {}
            };

            // Check each service
            if (this.services.whatsappClient) {
                status.services.whatsapp = this.services.whatsappClient.getStatus();
            }

            if (this.services.dbService) {
                status.services.database = await this.services.dbService.healthCheck();
            }

            if (this.services.aiAssistant) {
                status.services.ai = await this.services.aiAssistant.healthCheck();
            }

            if (this.services.vectorService) {
                status.services.vector = await this.services.vectorService.healthCheck();
            }

            if (this.services.contextService) {
                status.services.context = await this.services.contextService.healthCheck();
            }

            if (this.services.schedulerService) {
                status.services.scheduler = await this.services.schedulerService.healthCheck();
            }

            return status;

        } catch (error) {
            logger.error('Error getting system status:', error);
            return {
                initialized: this.isInitialized,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    async cleanup() {
        try {
            logger.info('Cleaning up services...');

            if (this.services.schedulerService) {
                await this.services.schedulerService.stopScheduler();
            }

            if (this.services.whatsappClient) {
                await this.services.whatsappClient.destroy();
            }

            if (this.services.telegramBot) {
                await this.services.telegramBot.stop();
            }

            // Close database connections
            const databaseConfig = require('./config/database');
            await databaseConfig.closeConnections();

            logger.info('✅ Cleanup completed');

        } catch (error) {
            logger.error('Error during cleanup:', error);
        }
    }

    // Graceful shutdown handler
    async shutdown(signal) {
        logger.info(`Received ${signal}, initiating graceful shutdown...`);
        
        try {
            await this.cleanup();
            logger.info('Graceful shutdown completed');
            process.exit(0);
        } catch (error) {
            logger.error('Error during shutdown:', error);
            process.exit(1);
        }
    }
}

// Create and start the application
const app = new WhatsAppAssistant();

// Handle process signals
process.on('SIGTERM', () => app.shutdown('SIGTERM'));
process.on('SIGINT', () => app.shutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    app.shutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    app.shutdown('unhandledRejection');
});

// Start the application
if (require.main === module) {
    app.initialize().then(() => {
        // Start WhatsApp client initialization in background
        app.startWhatsAppClient();
    }).catch((error) => {
        logger.error('Failed to start application:', error);
        process.exit(1);
    });
}

module.exports = WhatsAppAssistant;
