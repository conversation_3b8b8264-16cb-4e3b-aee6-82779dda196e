const winston = require('winston');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let metaStr = '';
        if (Object.keys(meta).length > 0) {
            metaStr = ' ' + JSON.stringify(meta, null, 2);
        }
        return `${timestamp} [${level}]: ${message}${metaStr}`;
    })
);

// Custom format for file output
const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { 
        service: 'whatsapp-assistant',
        version: process.env.npm_package_version || '1.0.0'
    },
    transports: [
        // Error log file
        new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true
        }),

        // Combined log file
        new winston.transports.File({
            filename: path.join(logsDir, 'combined.log'),
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 10,
            tailable: true
        }),

        // Daily rotating file
        new winston.transports.File({
            filename: path.join(logsDir, `app-${new Date().toISOString().split('T')[0]}.log`),
            maxsize: 50 * 1024 * 1024, // 50MB
            maxFiles: 30
        })
    ],
    
    // Handle uncaught exceptions
    exceptionHandlers: [
        new winston.transports.File({
            filename: path.join(logsDir, 'exceptions.log')
        })
    ],

    // Handle unhandled promise rejections
    rejectionHandlers: [
        new winston.transports.File({
            filename: path.join(logsDir, 'rejections.log')
        })
    ]
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: consoleFormat,
        level: 'debug'
    }));
} else {
    // In production, only log to console for errors
    logger.add(new winston.transports.Console({
        format: consoleFormat,
        level: 'error'
    }));
}

// Custom logging methods for specific use cases
logger.whatsapp = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'whatsapp' });
};

logger.telegram = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'telegram' });
};

logger.ai = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'ai' });
};

logger.database = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'database' });
};

logger.scheduler = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'scheduler' });
};

logger.api = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'api' });
};

// Performance logging
logger.performance = (operation, duration, meta = {}) => {
    logger.info(`Performance: ${operation} completed in ${duration}ms`, {
        ...meta,
        component: 'performance',
        operation,
        duration
    });
};

// Security logging
logger.security = (message, meta = {}) => {
    logger.warn(message, { ...meta, component: 'security' });
};

// Business logic logging
logger.business = (message, meta = {}) => {
    logger.info(message, { ...meta, component: 'business' });
};

// Create a stream for Morgan HTTP logging
logger.stream = {
    write: (message) => {
        logger.info(message.trim(), { component: 'http' });
    }
};

// Utility function to log function entry/exit
logger.trace = (functionName, args = {}) => {
    const startTime = Date.now();
    
    logger.debug(`Entering ${functionName}`, { 
        function: functionName, 
        args,
        component: 'trace'
    });
    
    return {
        exit: (result = null, error = null) => {
            const duration = Date.now() - startTime;
            
            if (error) {
                logger.error(`Exiting ${functionName} with error`, {
                    function: functionName,
                    duration,
                    error: error.message,
                    stack: error.stack,
                    component: 'trace'
                });
            } else {
                logger.debug(`Exiting ${functionName}`, {
                    function: functionName,
                    duration,
                    result: typeof result === 'object' ? 'object' : result,
                    component: 'trace'
                });
            }
        }
    };
};

// Log system information on startup
logger.info('Logger initialized', {
    level: logger.level,
    environment: process.env.NODE_ENV || 'development',
    logDirectory: logsDir,
    pid: process.pid,
    nodeVersion: process.version,
    platform: process.platform
});

module.exports = logger;
