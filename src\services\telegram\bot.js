const TelegramBot = require('node-telegram-bot-api');
const logger = require('../../utils/logger');
const DatabaseService = require('../database/dbService');

class TelegramBotService {
    constructor(token) {
        this.token = token;
        this.bot = null;
        this.aiAssistant = null;
        this.whatsappClient = null;
        this.dbService = new DatabaseService();
        this.authorizedUsers = this.getAuthorizedUsers();
        this.initialize();
    }

    getAuthorizedUsers() {
        const users = process.env.AUTHORIZED_TELEGRAM_USERS;
        if (!users) {
            logger.warn('No authorized Telegram users configured');
            return [];
        }
        return users.split(',').map(id => parseInt(id.trim()));
    }

    async initialize() {
        try {
            await this.dbService.initialize();
            
            this.bot = new TelegramBot(this.token, { 
                polling: true,
                request: {
                    agentOptions: {
                        keepAlive: true,
                        family: 4
                    }
                }
            });

            this.setupCommands();
            this.setupEventHandlers();
            
            logger.info('Telegram bot initialized successfully');
            console.log('✅ Telegram Bot is ready!');
        } catch (error) {
            logger.error('Failed to initialize Telegram bot:', error);
            throw error;
        }
    }

    setupCommands() {
        // Set bot commands
        this.bot.setMyCommands([
            { command: 'start', description: 'Start the bot and get help' },
            { command: 'help', description: 'Show available commands' },
            { command: 'status', description: 'Check WhatsApp connection status' },
            { command: 'chat', description: 'Chat with AI assistant' },
            { command: 'send', description: 'Send WhatsApp message' },
            { command: 'schedule', description: 'Schedule a WhatsApp message' },
            { command: 'search', description: 'Search message history' },
            { command: 'stats', description: 'Get message statistics' },
            { command: 'contacts', description: 'List WhatsApp contacts' },
            { command: 'chats', description: 'List WhatsApp chats' }
        ]);

        // Command handlers
        this.bot.onText(/\/start/, this.handleStart.bind(this));
        this.bot.onText(/\/help/, this.handleHelp.bind(this));
        this.bot.onText(/\/status/, this.handleStatus.bind(this));
        this.bot.onText(/\/chat (.+)/, this.handleChat.bind(this));
        this.bot.onText(/\/send (\+?\d+) (.+)/, this.handleSend.bind(this));
        this.bot.onText(/\/schedule (\+?\d+) (.+) at (.+)/, this.handleSchedule.bind(this));
        this.bot.onText(/\/search (.+)/, this.handleSearch.bind(this));
        this.bot.onText(/\/stats ?(\+?\d+)?/, this.handleStats.bind(this));
        this.bot.onText(/\/contacts/, this.handleContacts.bind(this));
        this.bot.onText(/\/chats/, this.handleChats.bind(this));
    }

    setupEventHandlers() {
        // Handle all messages
        this.bot.on('message', this.handleMessage.bind(this));

        // Handle callback queries (inline keyboard buttons)
        this.bot.on('callback_query', this.handleCallbackQuery.bind(this));

        // Handle errors
        this.bot.on('error', (error) => {
            logger.error('Telegram bot error:', error);
        });

        // Handle polling errors
        this.bot.on('polling_error', (error) => {
            logger.error('Telegram polling error:', error);
        });
    }

    // Check if user is authorized
    isAuthorized(userId) {
        return this.authorizedUsers.includes(userId);
    }

    // Send unauthorized message
    async sendUnauthorizedMessage(chatId) {
        await this.bot.sendMessage(
            chatId,
            '❌ You are not authorized to use this bot. Please contact the administrator.',
            { parse_mode: 'Markdown' }
        );
    }

    // Handle /start command
    async handleStart(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        const welcomeMessage = `
🤖 *WhatsApp Assistant Bot*

Welcome! I'm your WhatsApp AI assistant. I can help you:

• 💬 Send WhatsApp messages
• ⏰ Schedule messages
• 🔍 Search message history
• 📊 Get conversation statistics
• 🤖 Chat with AI assistant

Use /help to see all available commands.

*Status:* ${this.whatsappClient?.isReady ? '✅ Connected' : '❌ Disconnected'}
        `;

        await this.bot.sendMessage(chatId, welcomeMessage, { parse_mode: 'Markdown' });
    }

    // Handle /help command
    async handleHelp(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        const helpMessage = `
📚 *Available Commands:*

*Basic Commands:*
• /start - Start the bot
• /help - Show this help message
• /status - Check WhatsApp connection status

*Messaging:*
• /send <number> <message> - Send WhatsApp message
• /schedule <number> <message> at <datetime> - Schedule message

*AI Assistant:*
• /chat <message> - Chat with AI assistant

*Search & Stats:*
• /search <query> - Search message history
• /stats [number] - Get message statistics
• /contacts - List WhatsApp contacts
• /chats - List WhatsApp chats

*Examples:*
• \`/send +1234567890 Hello from Telegram!\`
• \`/schedule +1234567890 Meeting reminder at 2024-01-15 14:30\`
• \`/chat What messages did I receive today?\`
• \`/search important meeting\`
        `;

        await this.bot.sendMessage(chatId, helpMessage, { parse_mode: 'Markdown' });
    }

    // Handle /status command
    async handleStatus(msg) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        const whatsappStatus = this.whatsappClient?.getStatus();
        const dbHealth = await this.dbService.healthCheck();

        const statusMessage = `
📊 *System Status:*

*WhatsApp:* ${whatsappStatus?.isReady ? '✅ Connected' : '❌ Disconnected'}
*Database:* ${dbHealth.status === 'healthy' ? '✅ Healthy' : '❌ Unhealthy'}
*AI Assistant:* ${this.aiAssistant ? '✅ Available' : '❌ Unavailable'}

*WhatsApp Info:*
• QR Generated: ${whatsappStatus?.qrCodeGenerated ? 'Yes' : 'No'}
• Client Info: ${whatsappStatus?.clientInfo?.pushname || 'N/A'}

*Database:* ${dbHealth.timestamp || 'N/A'}
        `;

        await this.bot.sendMessage(chatId, statusMessage, { parse_mode: 'Markdown' });
    }

    // Handle /chat command
    async handleChat(msg, match) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        if (!this.aiAssistant) {
            return await this.bot.sendMessage(chatId, '❌ AI Assistant is not available');
        }

        const query = match[1];
        
        try {
            // Show typing indicator
            await this.bot.sendChatAction(chatId, 'typing');

            const response = await this.aiAssistant.processMessage(
                query,
                userId.toString(),
                'telegram',
                { chatId, userId }
            );

            if (response && response.text) {
                await this.bot.sendMessage(chatId, response.text, { parse_mode: 'Markdown' });
            } else {
                await this.bot.sendMessage(chatId, 'I couldn\'t generate a response. Please try again.');
            }
        } catch (error) {
            logger.error('Error processing chat command:', error);
            await this.bot.sendMessage(chatId, '❌ Error processing your request. Please try again.');
        }
    }

    // Handle /send command
    async handleSend(msg, match) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        if (!this.whatsappClient?.isReady) {
            return await this.bot.sendMessage(chatId, '❌ WhatsApp client is not ready');
        }

        const phoneNumber = match[1];
        const message = match[2];

        try {
            const result = await this.whatsappClient.sendMessage(phoneNumber, message);
            
            if (result.success) {
                await this.bot.sendMessage(
                    chatId,
                    `✅ Message sent successfully to ${phoneNumber}\n\n*Message:* ${message}`,
                    { parse_mode: 'Markdown' }
                );
            } else {
                await this.bot.sendMessage(chatId, '❌ Failed to send message');
            }
        } catch (error) {
            logger.error('Error sending WhatsApp message:', error);
            await this.bot.sendMessage(chatId, '❌ Error sending message: ' + error.message);
        }
    }

    // Handle /schedule command
    async handleSchedule(msg, match) {
        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return await this.sendUnauthorizedMessage(chatId);
        }

        const phoneNumber = match[1];
        const message = match[2];
        const datetime = match[3];

        try {
            const scheduledDate = new Date(datetime);
            
            if (scheduledDate <= new Date()) {
                return await this.bot.sendMessage(chatId, '❌ Scheduled time must be in the future');
            }

            const result = await this.dbService.saveScheduledMessage(
                phoneNumber,
                message,
                scheduledDate,
                userId.toString()
            );

            await this.bot.sendMessage(
                chatId,
                `✅ Message scheduled successfully!\n\n*To:* ${phoneNumber}\n*Message:* ${message}\n*Scheduled for:* ${scheduledDate.toLocaleString()}`,
                { parse_mode: 'Markdown' }
            );
        } catch (error) {
            logger.error('Error scheduling message:', error);
            await this.bot.sendMessage(chatId, '❌ Error scheduling message: ' + error.message);
        }
    }

    // Handle regular messages (not commands)
    async handleMessage(msg) {
        // Skip if it's a command (already handled)
        if (msg.text && msg.text.startsWith('/')) {
            return;
        }

        const chatId = msg.chat.id;
        const userId = msg.from.id;

        if (!this.isAuthorized(userId)) {
            return;
        }

        // If AI assistant is available, process as chat
        if (this.aiAssistant && msg.text) {
            try {
                await this.bot.sendChatAction(chatId, 'typing');

                const response = await this.aiAssistant.processMessage(
                    msg.text,
                    userId.toString(),
                    'telegram',
                    { chatId, userId }
                );

                if (response && response.text) {
                    await this.bot.sendMessage(chatId, response.text, { parse_mode: 'Markdown' });
                }
            } catch (error) {
                logger.error('Error processing message:', error);
            }
        }
    }

    // Handle callback queries
    async handleCallbackQuery(callbackQuery) {
        const chatId = callbackQuery.message.chat.id;
        const userId = callbackQuery.from.id;
        const data = callbackQuery.data;

        if (!this.isAuthorized(userId)) {
            return;
        }

        try {
            // Handle different callback data
            if (data.startsWith('send_')) {
                // Handle send message callbacks
                const phoneNumber = data.replace('send_', '');
                // Implementation for quick send
            }

            // Answer the callback query
            await this.bot.answerCallbackQuery(callbackQuery.id);
        } catch (error) {
            logger.error('Error handling callback query:', error);
        }
    }

    // Set AI Assistant
    setAIAssistant(aiAssistant) {
        this.aiAssistant = aiAssistant;
    }

    // Set WhatsApp Client
    setWhatsAppClient(whatsappClient) {
        this.whatsappClient = whatsappClient;
    }

    // Send notification to authorized users
    async sendNotification(message, parseMode = 'Markdown') {
        for (const userId of this.authorizedUsers) {
            try {
                await this.bot.sendMessage(userId, message, { parse_mode: parseMode });
            } catch (error) {
                logger.error(`Error sending notification to user ${userId}:`, error);
            }
        }
    }

    // Stop the bot
    async stop() {
        try {
            if (this.bot) {
                await this.bot.stopPolling();
                logger.info('Telegram bot stopped');
            }
        } catch (error) {
            logger.error('Error stopping Telegram bot:', error);
        }
    }
}

module.exports = TelegramBotService;
