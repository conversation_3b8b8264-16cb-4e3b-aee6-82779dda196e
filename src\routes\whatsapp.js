const express = require('express');
const router = express.Router();
const { apiKeyAuth, telegramAuth } = require('../middleware/auth');
const { validations } = require('../middleware/validation');
const logger = require('../utils/logger');

// WhatsApp routes
class WhatsAppRoutes {
    constructor(whatsappClient, dbService) {
        this.whatsappClient = whatsappClient;
        this.dbService = dbService;
        this.setupRoutes();
    }

    setupRoutes() {
        // Get WhatsApp status
        router.get('/status', apiKeyAuth, async (req, res) => {
            try {
                const status = this.whatsappClient.getStatus();
                const dbHealth = await this.dbService.healthCheck();

                res.json({
                    success: true,
                    data: {
                        whatsapp: status,
                        database: dbHealth,
                        timestamp: new Date().toISOString()
                    }
                });
            } catch (error) {
                logger.error('Error getting WhatsApp status:', error);
                res.status(500).json({
                    success: false,
                    error: 'Failed to get status'
                });
            }
        });

        // Send message
        router.post('/send', apiKeyAuth, validations.sendMessage, async (req, res) => {
            try {
                const { to, message } = req.body;

                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const result = await this.whatsappClient.sendMessage(to, message);

                res.json({
                    success: true,
                    data: result
                });

                logger.info('Message sent via API', {
                    to,
                    messageId: result.messageId
                });

            } catch (error) {
                logger.error('Error sending message via API:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Send media message
        router.post('/send-media', apiKeyAuth, async (req, res) => {
            try {
                const { to, mediaPath, caption } = req.body;

                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const result = await this.whatsappClient.sendMedia(to, mediaPath, caption);

                res.json({
                    success: true,
                    data: result
                });

                logger.info('Media message sent via API', {
                    to,
                    messageId: result.messageId
                });

            } catch (error) {
                logger.error('Error sending media message via API:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get message history
        router.get('/messages', apiKeyAuth, validations.queryParams.contact, async (req, res) => {
            try {
                const { contact, limit = 50, offset = 0 } = req.query;

                const messages = await this.dbService.getMessageHistory(
                    contact,
                    parseInt(limit),
                    parseInt(offset)
                );

                res.json({
                    success: true,
                    data: {
                        messages,
                        count: messages.length,
                        limit: parseInt(limit),
                        offset: parseInt(offset)
                    }
                });

            } catch (error) {
                logger.error('Error getting message history:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get today's messages
        router.get('/messages/today', apiKeyAuth, async (req, res) => {
            try {
                const { contact } = req.query;

                if (!contact) {
                    return res.status(400).json({
                        success: false,
                        error: 'Contact parameter required'
                    });
                }

                const messages = await this.dbService.getTodayMessages(contact);

                res.json({
                    success: true,
                    data: {
                        messages,
                        count: messages.length,
                        date: new Date().toDateString()
                    }
                });

            } catch (error) {
                logger.error('Error getting today messages:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get contacts
        router.get('/contacts', apiKeyAuth, async (req, res) => {
            try {
                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const contacts = await this.whatsappClient.getContacts();
                
                // Filter and format contacts
                const formattedContacts = contacts
                    .filter(contact => contact.isMyContact)
                    .map(contact => ({
                        id: contact.id._serialized,
                        name: contact.name || contact.pushname,
                        number: contact.number,
                        isBusiness: contact.isBusiness,
                        profilePicUrl: contact.profilePicUrl
                    }))
                    .slice(0, 100); // Limit to 100 contacts

                res.json({
                    success: true,
                    data: {
                        contacts: formattedContacts,
                        count: formattedContacts.length
                    }
                });

            } catch (error) {
                logger.error('Error getting contacts:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get chats
        router.get('/chats', apiKeyAuth, async (req, res) => {
            try {
                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const chats = await this.whatsappClient.getChats();
                
                // Format chats
                const formattedChats = chats
                    .map(chat => ({
                        id: chat.id._serialized,
                        name: chat.name,
                        isGroup: chat.isGroup,
                        isReadOnly: chat.isReadOnly,
                        unreadCount: chat.unreadCount,
                        lastMessage: chat.lastMessage ? {
                            body: chat.lastMessage.body,
                            timestamp: chat.lastMessage.timestamp,
                            from: chat.lastMessage.from
                        } : null
                    }))
                    .slice(0, 50); // Limit to 50 chats

                res.json({
                    success: true,
                    data: {
                        chats: formattedChats,
                        count: formattedChats.length
                    }
                });

            } catch (error) {
                logger.error('Error getting chats:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get specific chat
        router.get('/chats/:chatId', apiKeyAuth, async (req, res) => {
            try {
                const { chatId } = req.params;

                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const chat = await this.whatsappClient.getChat(chatId);

                res.json({
                    success: true,
                    data: {
                        id: chat.id._serialized,
                        name: chat.name,
                        isGroup: chat.isGroup,
                        isReadOnly: chat.isReadOnly,
                        unreadCount: chat.unreadCount,
                        participants: chat.participants?.map(p => ({
                            id: p.id._serialized,
                            isAdmin: p.isAdmin,
                            isSuperAdmin: p.isSuperAdmin
                        }))
                    }
                });

            } catch (error) {
                logger.error('Error getting chat:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get contact info
        router.get('/contacts/:contactId', apiKeyAuth, async (req, res) => {
            try {
                const { contactId } = req.params;

                if (!this.whatsappClient.isReady) {
                    return res.status(503).json({
                        success: false,
                        error: 'WhatsApp client is not ready'
                    });
                }

                const contact = await this.whatsappClient.getContact(contactId);

                res.json({
                    success: true,
                    data: {
                        id: contact.id._serialized,
                        name: contact.name || contact.pushname,
                        number: contact.number,
                        isBusiness: contact.isBusiness,
                        isEnterprise: contact.isEnterprise,
                        profilePicUrl: contact.profilePicUrl,
                        isMyContact: contact.isMyContact,
                        isUser: contact.isUser,
                        isWAContact: contact.isWAContact
                    }
                });

            } catch (error) {
                logger.error('Error getting contact:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Webhook endpoint for external integrations
        router.post('/webhook', async (req, res) => {
            try {
                const { event, data } = req.body;

                logger.info('Webhook received', { event, data });

                // Process webhook based on event type
                switch (event) {
                    case 'send_message':
                        if (this.whatsappClient.isReady) {
                            await this.whatsappClient.sendMessage(data.to, data.message);
                        }
                        break;
                    
                    default:
                        logger.warn('Unknown webhook event:', event);
                }

                res.json({
                    success: true,
                    message: 'Webhook processed'
                });

            } catch (error) {
                logger.error('Error processing webhook:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
    }

    getRouter() {
        return router;
    }
}

module.exports = WhatsAppRoutes;
