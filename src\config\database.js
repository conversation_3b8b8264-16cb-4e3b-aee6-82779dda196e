const { Pool } = require('pg');
const Redis = require('redis');
require('dotenv').config();

class DatabaseConfig {
    constructor() {
        this.pgPool = null;
        this.redisClient = null;
    }

    // PostgreSQL Configuration
    async initializePostgreSQL() {
        try {
            this.pgPool = new Pool({
                connectionString: process.env.DATABASE_URL,
                host: process.env.DB_HOST,
                port: process.env.DB_PORT,
                database: process.env.DB_NAME,
                user: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                max: 20,
                idleTimeoutMillis: 30000,
                connectionTimeoutMillis: 2000,
            });

            // Test connection
            const client = await this.pgPool.connect();
            console.log('PostgreSQL connected successfully');
            client.release();

            return this.pgPool;
        } catch (error) {
            console.error('PostgreSQL connection error:', error);
            throw error;
        }
    }

    // Redis Configuration
    async initializeRedis() {
        try {
            this.redisClient = Redis.createClient({
                url: process.env.REDIS_URL,
                host: process.env.REDIS_HOST,
                port: process.env.REDIS_PORT,
                password: process.env.REDIS_PASSWORD,
                retry_strategy: (options) => {
                    if (options.error && options.error.code === 'ECONNREFUSED') {
                        return new Error('The server refused the connection');
                    }
                    if (options.total_retry_time > 1000 * 60 * 60) {
                        return new Error('Retry time exhausted');
                    }
                    if (options.attempt > 10) {
                        return undefined;
                    }
                    return Math.min(options.attempt * 100, 3000);
                }
            });

            this.redisClient.on('error', (err) => {
                console.error('Redis Client Error:', err);
            });

            this.redisClient.on('connect', () => {
                console.log('Redis connected successfully');
            });

            await this.redisClient.connect();
            return this.redisClient;
        } catch (error) {
            console.error('Redis connection error:', error);
            throw error;
        }
    }

    // Get PostgreSQL pool
    getPostgreSQLPool() {
        if (!this.pgPool) {
            throw new Error('PostgreSQL not initialized. Call initializePostgreSQL() first.');
        }
        return this.pgPool;
    }

    // Get Redis client
    getRedisClient() {
        if (!this.redisClient) {
            throw new Error('Redis not initialized. Call initializeRedis() first.');
        }
        return this.redisClient;
    }

    // Close all connections
    async closeConnections() {
        try {
            if (this.pgPool) {
                await this.pgPool.end();
                console.log('PostgreSQL connection closed');
            }
            if (this.redisClient) {
                await this.redisClient.quit();
                console.log('Redis connection closed');
            }
        } catch (error) {
            console.error('Error closing database connections:', error);
        }
    }
}

module.exports = new DatabaseConfig();
