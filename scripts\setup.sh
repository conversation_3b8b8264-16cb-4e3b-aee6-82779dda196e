#!/bin/bash

# WhatsApp Assistant Setup Script
set -e

echo "🚀 Setting up WhatsApp AI Assistant..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if Node.js is installed
check_nodejs() {
    print_header "Checking Node.js installation..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_status "Node.js $(node --version) is installed ✓"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_status "npm $(npm --version) is installed ✓"
}

# Check if Docker is installed (optional)
check_docker() {
    print_header "Checking Docker installation (optional)..."
    
    if command -v docker &> /dev/null; then
        print_status "Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) is installed ✓"
        
        if command -v docker-compose &> /dev/null; then
            print_status "Docker Compose is installed ✓"
        else
            print_warning "Docker Compose is not installed. You can still run the app without Docker."
        fi
    else
        print_warning "Docker is not installed. You can still run the app without Docker."
    fi
}

# Create necessary directories
create_directories() {
    print_header "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p sessions
    mkdir -p database
    
    print_status "Directories created ✓"
}

# Copy environment file
setup_environment() {
    print_header "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_status "Environment file created from template ✓"
        print_warning "Please edit .env file with your configuration before running the application!"
    else
        print_status "Environment file already exists ✓"
    fi
}

# Install dependencies
install_dependencies() {
    print_header "Installing dependencies..."
    
    npm install
    
    print_status "Dependencies installed ✓"
}

# Setup database (if PostgreSQL is available)
setup_database() {
    print_header "Setting up database..."
    
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL client found. You can run the database schema manually:"
        print_status "psql -U your_user -d whatsapp_assistant -f database/schema.sql"
    else
        print_warning "PostgreSQL client not found. Please install PostgreSQL or use Docker."
    fi
}

# Run tests
run_tests() {
    print_header "Running tests..."
    
    if npm test; then
        print_status "All tests passed ✓"
    else
        print_warning "Some tests failed. Please check the output above."
    fi
}

# Main setup function
main() {
    print_header "=== WhatsApp AI Assistant Setup ==="
    echo ""
    
    check_nodejs
    check_npm
    check_docker
    echo ""
    
    create_directories
    setup_environment
    install_dependencies
    setup_database
    echo ""
    
    print_header "=== Setup Complete! ==="
    echo ""
    print_status "Next steps:"
    echo "1. Edit the .env file with your configuration"
    echo "2. Set up your database (PostgreSQL and Redis)"
    echo "3. Configure your API keys (OpenAI, Pinecone, Telegram)"
    echo "4. Run the application:"
    echo "   - Development: npm run dev"
    echo "   - Production: npm start"
    echo "   - Docker: docker-compose up -d"
    echo ""
    print_status "For more information, check the README.md file"
    echo ""
}

# Run setup if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
