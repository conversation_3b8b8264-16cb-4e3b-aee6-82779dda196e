# WhatsApp AI Assistant

A comprehensive WhatsApp-Telegram AI Assistant with RAG (Retrieval-Augmented Generation) capabilities, built with Node.js, OpenAI, and Pinecone.

## 🚀 Features

- **WhatsApp Integration**: Full WhatsApp Web client with message handling
- **Telegram Bot**: Complete Telegram bot for remote control and monitoring
- **AI Assistant**: OpenAI-powered conversational AI with function calling
- **RAG System**: Vector-based message search using Pinecone
- **Message Scheduling**: Schedule WhatsApp messages for later delivery
- **Context Management**: Intelligent conversation context with Redis caching
- **Database Storage**: PostgreSQL for persistent data storage
- **REST API**: Complete API for external integrations
- **Docker Support**: Full containerization with Docker Compose

## 📋 Prerequisites

- Node.js 18 or higher
- PostgreSQL 12 or higher
- Redis 6 or higher
- Docker and Docker Compose (optional)

## 🛠️ Installation

### Quick Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd whaAssist
```

2. Run the setup script:
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the application:
```bash
# Development
npm run dev

# Production
npm start

# Docker
docker-compose up -d
```

### Manual Setup

1. Install dependencies:
```bash
npm install
```

2. Set up the database:
```bash
# Create PostgreSQL database
createdb whatsapp_assistant

# Run schema
psql -d whatsapp_assistant -f database/schema.sql
```

3. Configure environment variables (see Configuration section)

4. Start services:
```bash
# Start Redis
redis-server

# Start the application
npm start
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./sessions

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
AUTHORIZED_TELEGRAM_USERS=123456789,987654321

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_INDEX=whatsapp-messages

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/whatsapp_assistant
REDIS_URL=redis://localhost:6379

# Server Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=/api/v1

# Security
JWT_SECRET=your_jwt_secret
API_KEY=your_api_key
```

### API Keys Setup

1. **OpenAI API Key**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
2. **Pinecone API Key**: Get from [Pinecone Console](https://app.pinecone.io/)
3. **Telegram Bot Token**: Create bot with [@BotFather](https://t.me/botfather)

## 🚀 Usage

### WhatsApp Setup

1. Start the application
2. Scan the QR code with your WhatsApp mobile app
3. The client will authenticate and start processing messages

### Telegram Commands

- `/start` - Initialize the bot
- `/status` - Check system status
- `/send <number> <message>` - Send WhatsApp message
- `/schedule <number> <message> at <datetime>` - Schedule message
- `/chat <message>` - Chat with AI assistant
- `/search <query>` - Search message history

### API Endpoints

#### WhatsApp API
- `GET /api/v1/whatsapp/status` - Get WhatsApp status
- `POST /api/v1/whatsapp/send` - Send message
- `GET /api/v1/whatsapp/messages` - Get message history
- `GET /api/v1/whatsapp/contacts` - Get contacts

#### AI API
- `POST /api/v1/ai/chat` - Chat with AI
- `POST /api/v1/ai/search` - Search messages with RAG
- `GET /api/v1/ai/context/:userId` - Get conversation context

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp      │    │   Telegram      │    │   REST API      │
│   Client        │    │   Bot           │    │   Server        │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │    AI Assistant         │
                    │  (OpenAI + Functions)   │
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
│   Vector DB     │    │   PostgreSQL    │    │   Redis Cache   │
│  (Pinecone)     │    │   Database      │    │   & Context     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🧪 Testing

Run tests:
```bash
# All tests
npm test

# Watch mode
npm run test:watch

# Coverage
npm run test:coverage
```

## 📊 Monitoring

### Health Checks
- Application: `GET /health`
- Detailed: `GET /api/v1/health/detailed`

### Logs
- Application logs: `logs/combined.log`
- Error logs: `logs/error.log`
- Daily logs: `logs/app-YYYY-MM-DD.log`

## 🐳 Docker Deployment

### Development
```bash
docker-compose up -d
```

### Production
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Services
- **App**: `localhost:3000`
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`
- **pgAdmin**: `localhost:8080` (with tools profile)

## 🔧 Development

### Project Structure
```
src/
├── config/          # Configuration files
├── services/        # Core services
│   ├── whatsapp/   # WhatsApp client
│   ├── telegram/   # Telegram bot
│   ├── ai/         # AI assistant
│   ├── database/   # Database services
│   └── scheduler/  # Message scheduling
├── routes/         # API routes
├── middleware/     # Express middleware
├── utils/          # Utilities
└── index.js        # Application entry point
```

### Adding New Features

1. Create service in `src/services/`
2. Add routes in `src/routes/`
3. Update main application in `src/index.js`
4. Add tests in `tests/`

## 🔒 Security

- API key authentication
- Rate limiting
- Input validation
- Helmet.js security headers
- Environment variable protection

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- Check logs in `logs/` directory
- Use health check endpoints
- Review environment configuration
- Check service status via Telegram bot

## 🔄 Updates

To update the application:
```bash
git pull origin main
npm install
npm run migrate  # If database changes
docker-compose down && docker-compose up -d  # For Docker
```
