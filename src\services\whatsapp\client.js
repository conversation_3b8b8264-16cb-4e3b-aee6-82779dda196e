const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');
const logger = require('../../utils/logger');
const WhatsAppMessageHandler = require('./messageHandler');

class WhatsAppClient {
    constructor() {
        this.client = null;
        this.messageHandler = null;
        this.aiAssistant = null;
        this.isReady = false;
        this.qrCodeGenerated = false;
        this.initializeClient();
    }

    initializeClient() {
        // Ensure sessions directory exists
        const sessionsPath = process.env.WHATSAPP_SESSION_PATH || './sessions';
        if (!fs.existsSync(sessionsPath)) {
            fs.mkdirSync(sessionsPath, { recursive: true });
        }

        this.client = new Client({
            authStrategy: new LocalAuth({
                dataPath: sessionsPath,
                clientId: 'whatsapp-assistant'
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            },
            webVersionCache: {
                type: 'remote',
                remotePath: 'https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html',
            }
        });

        this.messageHandler = new WhatsAppMessageHandler(this);
        this.setupEventHandlers();
    }

    setupEventHandlers() {
        // QR Code generation
        this.client.on('qr', (qr) => {
            if (!this.qrCodeGenerated) {
                console.log('\n=== WhatsApp QR Code ===');
                console.log('Scan this QR code with your WhatsApp mobile app:');
                qrcode.generate(qr, { small: true });
                console.log('========================\n');
                this.qrCodeGenerated = true;
                logger.info('WhatsApp QR code generated');
            }
        });

        // Client ready
        this.client.on('ready', () => {
            this.isReady = true;
            console.log('✅ WhatsApp Client is ready!');
            logger.info('WhatsApp client initialized successfully');
            this.logClientInfo();
        });

        // Authentication success
        this.client.on('authenticated', () => {
            console.log('✅ WhatsApp Client authenticated successfully');
            logger.info('WhatsApp client authenticated');
        });

        // Authentication failure
        this.client.on('auth_failure', (msg) => {
            console.error('❌ WhatsApp authentication failed:', msg);
            logger.error('WhatsApp authentication failed', { error: msg });
        });

        // Client disconnected
        this.client.on('disconnected', (reason) => {
            console.log('⚠️ WhatsApp Client disconnected:', reason);
            logger.warn('WhatsApp client disconnected', { reason });
            this.isReady = false;
        });

        // Message received
        this.client.on('message', async (message) => {
            try {
                await this.messageHandler.handleIncomingMessage(message);
            } catch (error) {
                logger.error('Error handling incoming message:', error);
            }
        });

        // Message creation (sent by us)
        this.client.on('message_create', async (message) => {
            try {
                if (message.fromMe) {
                    await this.messageHandler.handleOutgoingMessage(message);
                }
            } catch (error) {
                logger.error('Error handling outgoing message:', error);
            }
        });

        // Group join
        this.client.on('group_join', async (notification) => {
            try {
                logger.info('User joined group', {
                    chatId: notification.chatId,
                    who: notification.who
                });
            } catch (error) {
                logger.error('Error handling group join:', error);
            }
        });

        // Group leave
        this.client.on('group_leave', async (notification) => {
            try {
                logger.info('User left group', {
                    chatId: notification.chatId,
                    who: notification.who
                });
            } catch (error) {
                logger.error('Error handling group leave:', error);
            }
        });

        // Contact changed
        this.client.on('contact_changed', async (message, oldId, newId, isContact) => {
            try {
                logger.info('Contact changed', { oldId, newId, isContact });
            } catch (error) {
                logger.error('Error handling contact change:', error);
            }
        });
    }

    async logClientInfo() {
        try {
            const info = this.client.info;
            if (info) {
                logger.info('WhatsApp client info', {
                    wid: info.wid._serialized,
                    pushname: info.pushname,
                    platform: info.platform
                });
            }
        } catch (error) {
            logger.error('Error getting client info:', error);
        }
    }

    // Initialize the client
    async initialize() {
        try {
            console.log('🚀 Initializing WhatsApp Client...');
            await this.client.initialize();
        } catch (error) {
            logger.error('Failed to initialize WhatsApp client:', error);
            throw error;
        }
    }

    // Send text message
    async sendMessage(to, message) {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            const chatId = to.includes('@c.us') ? to : `${to}@c.us`;
            const sentMessage = await this.client.sendMessage(chatId, message);
            
            logger.info('Message sent successfully', {
                to: chatId,
                messageId: sentMessage.id._serialized
            });
            
            return {
                success: true,
                messageId: sentMessage.id._serialized,
                timestamp: sentMessage.timestamp
            };
        } catch (error) {
            logger.error('Error sending message:', error);
            throw error;
        }
    }

    // Send media message
    async sendMedia(to, mediaPath, caption = '') {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            const chatId = to.includes('@c.us') ? to : `${to}@c.us`;
            const media = MessageMedia.fromFilePath(mediaPath);

            const sentMessage = await this.client.sendMessage(chatId, media, {
                caption: caption
            });

            logger.info('Media message sent successfully', {
                to: chatId,
                messageId: sentMessage.id._serialized,
                mediaType: media.mimetype
            });

            return {
                success: true,
                messageId: sentMessage.id._serialized,
                timestamp: sentMessage.timestamp
            };
        } catch (error) {
            logger.error('Error sending media message:', error);
            throw error;
        }
    }

    // Reply to a message
    async replyToMessage(message, replyText) {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            const sentMessage = await message.reply(replyText);

            logger.info('Reply sent successfully', {
                originalMessageId: message.id._serialized,
                replyMessageId: sentMessage.id._serialized
            });

            return {
                success: true,
                messageId: sentMessage.id._serialized,
                timestamp: sentMessage.timestamp
            };
        } catch (error) {
            logger.error('Error sending reply:', error);
            throw error;
        }
    }

    // Get chat by ID
    async getChat(chatId) {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            return await this.client.getChatById(chatId);
        } catch (error) {
            logger.error('Error getting chat:', error);
            throw error;
        }
    }

    // Get contact by ID
    async getContact(contactId) {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            return await this.client.getContactById(contactId);
        } catch (error) {
            logger.error('Error getting contact:', error);
            throw error;
        }
    }

    // Get all chats
    async getChats() {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            return await this.client.getChats();
        } catch (error) {
            logger.error('Error getting chats:', error);
            throw error;
        }
    }

    // Get all contacts
    async getContacts() {
        if (!this.isReady) {
            throw new Error('WhatsApp client is not ready');
        }

        try {
            return await this.client.getContacts();
        } catch (error) {
            logger.error('Error getting contacts:', error);
            throw error;
        }
    }

    // Set AI Assistant
    setAIAssistant(aiAssistant) {
        this.aiAssistant = aiAssistant;
        this.messageHandler.setAIAssistant(aiAssistant);
    }

    // Get client status
    getStatus() {
        return {
            isReady: this.isReady,
            qrCodeGenerated: this.qrCodeGenerated,
            clientInfo: this.client?.info || null
        };
    }

    // Destroy client
    async destroy() {
        try {
            if (this.client) {
                await this.client.destroy();
                logger.info('WhatsApp client destroyed');
            }
        } catch (error) {
            logger.error('Error destroying WhatsApp client:', error);
        }
    }
}

module.exports = WhatsAppClient;
