const databaseConfig = require('../../config/database');
const DatabaseService = require('../database/dbService');
const logger = require('../../utils/logger');

class ContextService {
    constructor() {
        this.redisClient = null;
        this.dbService = new DatabaseService();
        this.maxContextMessages = parseInt(process.env.MAX_CONTEXT_MESSAGES) || 50;
        this.contextCacheTTL = parseInt(process.env.CONTEXT_CACHE_TTL) || 3600; // 1 hour
    }

    async initialize() {
        try {
            this.redisClient = await databaseConfig.initializeRedis();
            await this.dbService.initialize();
            
            logger.info('Context service initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize context service:', error);
            throw error;
        }
    }

    // Get conversation context for a user
    async getConversationContext(userId, platform) {
        try {
            const cacheKey = this.getContextCacheKey(userId, platform);
            
            // Try to get from Redis cache first
            let context = await this.redisClient.get(cacheKey);
            
            if (context) {
                context = JSON.parse(context);
                logger.debug('Context retrieved from cache', { userId, platform });
            } else {
                // Get from database
                context = await this.getContextFromDatabase(userId, platform);
                
                // Cache for future use
                if (context.length > 0) {
                    await this.redisClient.setEx(
                        cacheKey,
                        this.contextCacheTTL,
                        JSON.stringify(context)
                    );
                }
                
                logger.debug('Context retrieved from database', { userId, platform });
            }
            
            return context;
        } catch (error) {
            logger.error('Error getting conversation context:', error);
            return [];
        }
    }

    // Get context from database
    async getContextFromDatabase(userId, platform) {
        try {
            // Get recent AI conversations
            const result = await this.dbService.pool.query(`
                SELECT conversation_context, last_interaction
                FROM ai_conversations 
                WHERE user_id = $1 AND platform = $2
                ORDER BY last_interaction DESC
                LIMIT 1
            `, [userId, platform]);

            if (result.rows.length === 0) {
                return [];
            }

            const conversationData = result.rows[0];
            const context = conversationData.conversation_context || {};
            
            // Return recent messages from context
            return context.recentMessages || [];
        } catch (error) {
            logger.error('Error getting context from database:', error);
            return [];
        }
    }

    // Add message to conversation context
    async addToConversationContext(userId, platform, userMessage, aiResponse) {
        try {
            const cacheKey = this.getContextCacheKey(userId, platform);
            
            // Get current context
            let context = await this.getConversationContext(userId, platform);
            
            // Add new message pair
            const newContextItem = {
                userMessage,
                aiResponse,
                timestamp: new Date().toISOString()
            };
            
            context.push(newContextItem);
            
            // Keep only recent messages (limit context size)
            if (context.length > this.maxContextMessages) {
                context = context.slice(-this.maxContextMessages);
            }
            
            // Update cache
            await this.redisClient.setEx(
                cacheKey,
                this.contextCacheTTL,
                JSON.stringify(context)
            );
            
            // Update database
            await this.updateDatabaseContext(userId, platform, context);
            
            logger.debug('Context updated', { 
                userId, 
                platform, 
                contextSize: context.length 
            });
            
        } catch (error) {
            logger.error('Error adding to conversation context:', error);
        }
    }

    // Update database with context
    async updateDatabaseContext(userId, platform, context) {
        try {
            const contextData = {
                recentMessages: context,
                lastUpdated: new Date().toISOString(),
                messageCount: context.length
            };

            await this.dbService.pool.query(`
                INSERT INTO ai_conversations (user_id, platform, conversation_context, last_interaction, total_messages)
                VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4)
                ON CONFLICT (user_id, platform) DO UPDATE SET
                    conversation_context = EXCLUDED.conversation_context,
                    last_interaction = CURRENT_TIMESTAMP,
                    total_messages = ai_conversations.total_messages + 1,
                    updated_at = CURRENT_TIMESTAMP
            `, [userId, platform, JSON.stringify(contextData), context.length]);

        } catch (error) {
            logger.error('Error updating database context:', error);
        }
    }

    // Get today's messages for context
    async getTodayMessages(userId, platform) {
        try {
            const cacheKey = this.getTodayCacheKey(userId, platform);
            
            // Try cache first
            let messages = await this.redisClient.get(cacheKey);
            
            if (messages) {
                messages = JSON.parse(messages);
            } else {
                // Get from database
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                let query, params;
                
                if (platform === 'whatsapp') {
                    query = `
                        SELECT * FROM messages 
                        WHERE (from_number = $1 OR to_number = $1)
                        AND timestamp >= $2
                        ORDER BY timestamp ASC
                        LIMIT 100
                    `;
                    params = [userId, today.getTime()];
                } else {
                    // For Telegram, we might not have direct message history
                    // Get from AI conversations
                    query = `
                        SELECT conversation_context FROM ai_conversations
                        WHERE user_id = $1 AND platform = $2
                        AND last_interaction >= $3
                    `;
                    params = [userId, platform, today];
                }
                
                const result = await this.dbService.pool.query(query, params);
                messages = result.rows;
                
                // Cache for shorter time (30 minutes for today's data)
                await this.redisClient.setEx(
                    cacheKey,
                    1800,
                    JSON.stringify(messages)
                );
            }
            
            return messages;
        } catch (error) {
            logger.error('Error getting today messages:', error);
            return [];
        }
    }

    // Get message context around a specific message
    async getMessageContext(messageId, contextWindow = 5) {
        try {
            // Get the target message
            const messageResult = await this.dbService.pool.query(
                'SELECT * FROM messages WHERE id = $1',
                [messageId]
            );

            if (messageResult.rows.length === 0) {
                return null;
            }

            const targetMessage = messageResult.rows[0];
            const timestamp = targetMessage.timestamp;
            const chatId = targetMessage.chat_id;

            // Get surrounding messages
            const contextResult = await this.dbService.pool.query(`
                SELECT * FROM messages 
                WHERE chat_id = $1 
                AND timestamp BETWEEN $2 AND $3
                ORDER BY timestamp ASC
            `, [
                chatId,
                timestamp - (contextWindow * 60000), // N minutes before
                timestamp + (contextWindow * 60000)  // N minutes after
            ]);

            return {
                targetMessage,
                contextMessages: contextResult.rows,
                totalContext: contextResult.rows.length
            };

        } catch (error) {
            logger.error('Error getting message context:', error);
            return null;
        }
    }

    // Clear context for a user
    async clearContext(userId, platform) {
        try {
            const cacheKey = this.getContextCacheKey(userId, platform);
            const todayCacheKey = this.getTodayCacheKey(userId, platform);
            
            // Clear from cache
            await this.redisClient.del(cacheKey);
            await this.redisClient.del(todayCacheKey);
            
            // Clear from database
            await this.dbService.pool.query(`
                UPDATE ai_conversations 
                SET conversation_context = '{}', updated_at = CURRENT_TIMESTAMP
                WHERE user_id = $1 AND platform = $2
            `, [userId, platform]);
            
            logger.info('Context cleared', { userId, platform });
            
        } catch (error) {
            logger.error('Error clearing context:', error);
        }
    }

    // Get context summary for a user
    async getContextSummary(userId, platform) {
        try {
            const context = await this.getConversationContext(userId, platform);
            const todayMessages = await this.getTodayMessages(userId, platform);
            
            // Get conversation stats from database
            const statsResult = await this.dbService.pool.query(`
                SELECT 
                    total_messages,
                    last_interaction,
                    created_at
                FROM ai_conversations 
                WHERE user_id = $1 AND platform = $2
            `, [userId, platform]);

            const stats = statsResult.rows[0] || {};

            return {
                contextSize: context.length,
                todayMessagesCount: todayMessages.length,
                totalConversations: stats.total_messages || 0,
                lastInteraction: stats.last_interaction,
                firstInteraction: stats.created_at,
                cacheStatus: await this.getCacheStatus(userId, platform)
            };

        } catch (error) {
            logger.error('Error getting context summary:', error);
            return null;
        }
    }

    // Get cache status
    async getCacheStatus(userId, platform) {
        try {
            const contextKey = this.getContextCacheKey(userId, platform);
            const todayKey = this.getTodayCacheKey(userId, platform);
            
            const [contextTTL, todayTTL] = await Promise.all([
                this.redisClient.ttl(contextKey),
                this.redisClient.ttl(todayKey)
            ]);

            return {
                contextCached: contextTTL > 0,
                contextTTL: contextTTL > 0 ? contextTTL : null,
                todayCached: todayTTL > 0,
                todayTTL: todayTTL > 0 ? todayTTL : null
            };

        } catch (error) {
            logger.error('Error getting cache status:', error);
            return null;
        }
    }

    // Cleanup old context data
    async cleanupOldContext(daysToKeep = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            // Clean up old AI conversations
            const result = await this.dbService.pool.query(`
                DELETE FROM ai_conversations 
                WHERE last_interaction < $1
                RETURNING user_id, platform
            `, [cutoffDate]);

            // Clear corresponding cache entries
            for (const row of result.rows) {
                const contextKey = this.getContextCacheKey(row.user_id, row.platform);
                const todayKey = this.getTodayCacheKey(row.user_id, row.platform);
                
                await this.redisClient.del(contextKey);
                await this.redisClient.del(todayKey);
            }

            logger.info('Old context data cleaned up', {
                deletedCount: result.rowCount,
                cutoffDate: cutoffDate.toISOString()
            });

            return result.rowCount;

        } catch (error) {
            logger.error('Error cleaning up old context:', error);
            return 0;
        }
    }

    // Generate cache keys
    getContextCacheKey(userId, platform) {
        return `context:${platform}:${userId}`;
    }

    getTodayCacheKey(userId, platform) {
        const today = new Date().toISOString().split('T')[0];
        return `today:${platform}:${userId}:${today}`;
    }

    getDateKey() {
        return new Date().toISOString().split('T')[0];
    }

    // Health check
    async healthCheck() {
        try {
            // Test Redis connection
            await this.redisClient.ping();
            
            // Test database connection
            const dbHealth = await this.dbService.healthCheck();
            
            return {
                status: 'healthy',
                redis: 'connected',
                database: dbHealth.status,
                maxContextMessages: this.maxContextMessages,
                contextCacheTTL: this.contextCacheTTL
            };

        } catch (error) {
            logger.error('Context service health check failed:', error);
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }
}

module.exports = ContextService;
