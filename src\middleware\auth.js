const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

// Simple API key authentication middleware
const apiKeyAuth = (req, res, next) => {
    const apiKey = req.headers['x-api-key'] || req.query.apiKey;
    const validApiKey = process.env.API_KEY;

    if (!validApiKey) {
        logger.warn('API_KEY not configured');
        return res.status(500).json({ error: 'Server configuration error' });
    }

    if (!apiKey) {
        return res.status(401).json({ error: 'API key required' });
    }

    if (apiKey !== validApiKey) {
        logger.warn('Invalid API key attempt', { 
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        return res.status(401).json({ error: 'Invalid API key' });
    }

    next();
};

// JWT authentication middleware
const jwtAuth = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1]; // Bearer token

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        logger.warn('Invalid JWT token', { 
            error: error.message,
            ip: req.ip 
        });
        return res.status(401).json({ error: 'Invalid or expired token' });
    }
};

// Telegram user authentication
const telegramAuth = (req, res, next) => {
    const userId = req.body.userId || req.query.userId;
    const authorizedUsers = process.env.AUTHORIZED_TELEGRAM_USERS?.split(',').map(id => parseInt(id.trim())) || [];

    if (!userId) {
        return res.status(401).json({ error: 'User ID required' });
    }

    if (!authorizedUsers.includes(parseInt(userId))) {
        logger.warn('Unauthorized Telegram user attempt', { 
            userId,
            ip: req.ip 
        });
        return res.status(403).json({ error: 'User not authorized' });
    }

    req.telegramUserId = parseInt(userId);
    next();
};

// Optional authentication - allows both authenticated and unauthenticated requests
const optionalAuth = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (token) {
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            req.user = decoded;
        } catch (error) {
            // Token is invalid, but we continue without authentication
            logger.debug('Invalid token in optional auth', { error: error.message });
        }
    }
    
    next();
};

// Generate JWT token
const generateToken = (payload, expiresIn = '24h') => {
    return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

// Verify JWT token
const verifyToken = (token) => {
    try {
        return jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
        throw new Error('Invalid token');
    }
};

module.exports = {
    apiKeyAuth,
    jwtAuth,
    telegramAuth,
    optionalAuth,
    generateToken,
    verifyToken
};
