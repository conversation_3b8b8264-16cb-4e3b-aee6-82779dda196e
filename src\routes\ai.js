const express = require('express');
const router = express.Router();
const { apiKeyAuth, telegramAuth } = require('../middleware/auth');
const { validations } = require('../middleware/validation');
const logger = require('../utils/logger');

// AI routes
class AIRoutes {
    constructor(aiAssistant, vectorService, contextService) {
        this.aiAssistant = aiAssistant;
        this.vectorService = vectorService;
        this.contextService = contextService;
        this.setupRoutes();
    }

    setupRoutes() {
        // Chat with AI
        router.post('/chat', apiKeyAuth, validations.aiChat, async (req, res) => {
            try {
                const { message, userId, platform = 'api' } = req.body;

                const response = await this.aiAssistant.processMessage(
                    message,
                    userId,
                    platform,
                    { apiRequest: true }
                );

                res.json({
                    success: true,
                    data: {
                        response: response.text,
                        functionCalls: response.functionCalls,
                        timestamp: new Date().toISOString()
                    }
                });

                logger.info('AI chat processed via API', {
                    userId,
                    platform,
                    messageLength: message.length
                });

            } catch (error) {
                logger.error('Error processing AI chat:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Search messages using RAG
        router.post('/search', apiKeyAuth, validations.searchMessages, async (req, res) => {
            try {
                const { query, contact, limit = 10, minScore = 0.7 } = req.body;

                let results;
                if (contact) {
                    results = await this.vectorService.searchMessagesByContact(
                        query,
                        contact,
                        { topK: limit, minScore }
                    );
                } else {
                    results = await this.vectorService.searchSimilarMessages(
                        query,
                        { topK: limit, minScore }
                    );
                }

                res.json({
                    success: true,
                    data: {
                        query,
                        results: results.map(r => ({
                            messageId: r.messageId,
                            from: r.contactName || r.from,
                            body: r.body,
                            timestamp: new Date(r.timestamp).toISOString(),
                            score: r.score,
                            isGroup: r.isGroup,
                            chatId: r.chatId
                        })),
                        count: results.length,
                        minScore
                    }
                });

                logger.info('Message search completed via API', {
                    query: query.substring(0, 50),
                    resultsCount: results.length
                });

            } catch (error) {
                logger.error('Error searching messages:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get conversation context
        router.get('/context/:userId', apiKeyAuth, async (req, res) => {
            try {
                const { userId } = req.params;
                const { platform = 'whatsapp' } = req.query;

                const context = await this.contextService.getConversationContext(userId, platform);
                const summary = await this.contextService.getContextSummary(userId, platform);

                res.json({
                    success: true,
                    data: {
                        userId,
                        platform,
                        context,
                        summary,
                        timestamp: new Date().toISOString()
                    }
                });

            } catch (error) {
                logger.error('Error getting conversation context:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Clear conversation context
        router.delete('/context/:userId', apiKeyAuth, async (req, res) => {
            try {
                const { userId } = req.params;
                const { platform = 'whatsapp' } = req.query;

                await this.contextService.clearContext(userId, platform);

                res.json({
                    success: true,
                    message: 'Context cleared successfully',
                    data: {
                        userId,
                        platform,
                        timestamp: new Date().toISOString()
                    }
                });

                logger.info('Context cleared via API', { userId, platform });

            } catch (error) {
                logger.error('Error clearing context:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get AI assistant status
        router.get('/status', apiKeyAuth, async (req, res) => {
            try {
                const aiHealth = await this.aiAssistant.healthCheck();
                const vectorHealth = await this.vectorService.healthCheck();
                const contextHealth = await this.contextService.healthCheck();

                res.json({
                    success: true,
                    data: {
                        ai: aiHealth,
                        vector: vectorHealth,
                        context: contextHealth,
                        timestamp: new Date().toISOString()
                    }
                });

            } catch (error) {
                logger.error('Error getting AI status:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get vector database statistics
        router.get('/vector/stats', apiKeyAuth, async (req, res) => {
            try {
                const stats = await this.vectorService.getVectorStats();

                res.json({
                    success: true,
                    data: stats
                });

            } catch (error) {
                logger.error('Error getting vector stats:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Store message embedding manually
        router.post('/embeddings', apiKeyAuth, async (req, res) => {
            try {
                const { messageData } = req.body;

                if (!messageData || !messageData.body) {
                    return res.status(400).json({
                        success: false,
                        error: 'Message data with body required'
                    });
                }

                const vectorId = await this.vectorService.storeMessage(messageData);

                res.json({
                    success: true,
                    data: {
                        vectorId,
                        messageId: messageData.id,
                        timestamp: new Date().toISOString()
                    }
                });

                logger.info('Embedding stored via API', {
                    messageId: messageData.id,
                    vectorId
                });

            } catch (error) {
                logger.error('Error storing embedding:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Search messages by date range
        router.post('/search/date-range', apiKeyAuth, async (req, res) => {
            try {
                const { query, startDate, endDate, limit = 10, minScore = 0.7 } = req.body;

                if (!query || !startDate || !endDate) {
                    return res.status(400).json({
                        success: false,
                        error: 'Query, startDate, and endDate are required'
                    });
                }

                const results = await this.vectorService.searchMessagesByDateRange(
                    query,
                    startDate,
                    endDate,
                    { topK: limit, minScore }
                );

                res.json({
                    success: true,
                    data: {
                        query,
                        dateRange: { startDate, endDate },
                        results: results.map(r => ({
                            messageId: r.messageId,
                            from: r.contactName || r.from,
                            body: r.body,
                            timestamp: new Date(r.timestamp).toISOString(),
                            score: r.score,
                            isGroup: r.isGroup
                        })),
                        count: results.length
                    }
                });

            } catch (error) {
                logger.error('Error searching messages by date range:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get message context around a specific message
        router.get('/messages/:messageId/context', apiKeyAuth, async (req, res) => {
            try {
                const { messageId } = req.params;
                const { contextSize = 5 } = req.query;

                const context = await this.vectorService.getMessageContext(
                    messageId,
                    parseInt(contextSize)
                );

                if (!context) {
                    return res.status(404).json({
                        success: false,
                        error: 'Message not found'
                    });
                }

                res.json({
                    success: true,
                    data: context
                });

            } catch (error) {
                logger.error('Error getting message context:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Batch process messages for embeddings
        router.post('/embeddings/batch', apiKeyAuth, async (req, res) => {
            try {
                const { messages } = req.body;

                if (!Array.isArray(messages) || messages.length === 0) {
                    return res.status(400).json({
                        success: false,
                        error: 'Array of messages required'
                    });
                }

                const vectorIds = await this.vectorService.storeMessagesBatch(messages);

                res.json({
                    success: true,
                    data: {
                        processedCount: vectorIds.length,
                        vectorIds,
                        timestamp: new Date().toISOString()
                    }
                });

                logger.info('Batch embeddings stored via API', {
                    count: vectorIds.length
                });

            } catch (error) {
                logger.error('Error storing batch embeddings:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Cleanup old embeddings
        router.delete('/embeddings/cleanup', apiKeyAuth, async (req, res) => {
            try {
                const { daysToKeep = 30 } = req.body;

                const deletedCount = await this.vectorService.cleanupOldEmbeddings(daysToKeep);

                res.json({
                    success: true,
                    data: {
                        deletedCount,
                        daysToKeep,
                        timestamp: new Date().toISOString()
                    }
                });

                logger.info('Embeddings cleanup completed via API', {
                    deletedCount,
                    daysToKeep
                });

            } catch (error) {
                logger.error('Error cleaning up embeddings:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
    }

    getRouter() {
        return router;
    }
}

module.exports = AIRoutes;
