version: '3.8'

services:
  # Main application
  whatsapp-assistant:
    build: .
    container_name: whatsapp-assistant
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**********************************************************/whatsapp_assistant
      - REDIS_URL=redis://redis:6379
      - WHATSAPP_SESSION_PATH=/app/sessions
    volumes:
      - ./sessions:/app/sessions
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - whatsapp-network
    env_file:
      - .env

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: whatsapp-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: whatsapp_assistant
      POSTGRES_USER: whatsapp_user
      POSTGRES_PASSWORD: whatsapp_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    ports:
      - "5432:5432"
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U whatsapp_user -d whatsapp_assistant"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: whatsapp-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # pgAdmin (optional, for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: whatsapp-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - whatsapp-network
    profiles:
      - tools

  # Redis Commander (optional, for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: whatsapp-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - whatsapp-network
    profiles:
      - tools

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: whatsapp-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - whatsapp-assistant
    networks:
      - whatsapp-network
    profiles:
      - proxy

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  whatsapp-network:
    driver: bridge
