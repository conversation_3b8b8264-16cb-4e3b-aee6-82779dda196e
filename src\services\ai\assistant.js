const OpenAI = require('openai');
const VectorService = require('../database/vectorService');
const ContextService = require('./contextService');
const DatabaseService = require('../database/dbService');
const logger = require('../../utils/logger');

class AIAssistant {
    constructor() {
        this.openai = null;
        this.vectorService = new VectorService();
        this.contextService = new ContextService();
        this.dbService = new DatabaseService();
        this.whatsappClient = null;
        this.telegramBot = null;
        this.model = process.env.OPENAI_MODEL || 'gpt-4';
        this.maxTokens = 2000;
        this.temperature = 0.7;
    }

    async initialize() {
        try {
            // Initialize OpenAI
            this.openai = new OpenAI({
                apiKey: process.env.OPENAI_API_KEY,
            });

            // Initialize services
            await this.vectorService.initialize();
            await this.contextService.initialize();
            await this.dbService.initialize();

            logger.info('AI Assistant initialized successfully', {
                model: this.model
            });

        } catch (error) {
            logger.error('Failed to initialize AI Assistant:', error);
            throw error;
        }
    }

    // Main message processing function
    async processMessage(message, userId, platform, context = {}) {
        try {
            logger.info('Processing message with AI', {
                userId,
                platform,
                messageLength: message.length
            });

            // Get conversation context
            const conversationContext = await this.contextService.getConversationContext(
                userId,
                platform
            );

            // Search for relevant historical messages using RAG
            const relevantHistory = await this.vectorService.searchSimilarMessages(
                message,
                { topK: 5, minScore: 0.75 }
            );

            // Build the prompt with context
            const prompt = await this.buildPrompt(
                message,
                conversationContext,
                relevantHistory,
                context
            );

            // Get AI response
            const response = await this.openai.chat.completions.create({
                model: this.model,
                messages: prompt,
                max_tokens: this.maxTokens,
                temperature: this.temperature,
                functions: this.getFunctionDefinitions(),
                function_call: "auto"
            });

            // Process the response
            const processedResponse = await this.processResponse(
                response,
                userId,
                platform,
                context
            );

            // Update conversation context
            await this.contextService.addToConversationContext(
                userId,
                platform,
                message,
                processedResponse.text
            );

            // Save AI conversation to database
            await this.dbService.saveAIConversation(
                userId,
                platform,
                {
                    userMessage: message,
                    aiResponse: processedResponse.text,
                    functionCalls: processedResponse.functionCalls,
                    timestamp: new Date().toISOString()
                }
            );

            return processedResponse;

        } catch (error) {
            logger.error('Error processing message with AI:', error);
            throw error;
        }
    }

    // Build conversation prompt with context
    async buildPrompt(message, conversationContext, relevantHistory, additionalContext) {
        const systemPrompt = this.getSystemPrompt(additionalContext);
        
        const messages = [
            { role: "system", content: systemPrompt }
        ];

        // Add relevant historical context
        if (relevantHistory && relevantHistory.length > 0) {
            const historyContext = this.formatHistoricalContext(relevantHistory);
            messages.push({
                role: "system",
                content: `Relevant conversation history:\n${historyContext}`
            });
        }

        // Add recent conversation context
        if (conversationContext && conversationContext.length > 0) {
            conversationContext.forEach(ctx => {
                messages.push({ role: "user", content: ctx.userMessage });
                if (ctx.aiResponse) {
                    messages.push({ role: "assistant", content: ctx.aiResponse });
                }
            });
        }

        // Add current message
        messages.push({ role: "user", content: message });

        return messages;
    }

    // Get system prompt
    getSystemPrompt(context = {}) {
        const { platform, isGroup, contactName } = context;
        
        return `You are an intelligent WhatsApp assistant that helps manage messages and conversations. You have access to message history and can perform various actions.

Your capabilities:
- Send WhatsApp messages to contacts
- Schedule messages for later delivery
- Search through message history
- Provide conversation insights and summaries
- Answer questions about past conversations

Context:
- Platform: ${platform || 'unknown'}
- Contact: ${contactName || 'unknown'}
- Group chat: ${isGroup ? 'yes' : 'no'}
- Current time: ${new Date().toISOString()}

Guidelines:
- Be helpful, concise, and professional
- Use the available functions when appropriate
- Provide accurate information based on message history
- Respect privacy and don't share sensitive information
- If you need to send a message, use the send_whatsapp_message function
- If you need to schedule a message, use the schedule_message function
- If you need to search messages, use the search_messages function

Always respond in a helpful and natural way.`;
    }

    // Format historical context for prompt
    formatHistoricalContext(relevantHistory) {
        return relevantHistory
            .map(msg => {
                const date = new Date(msg.timestamp).toLocaleString();
                const from = msg.contactName || msg.from;
                return `[${date}] ${from}: ${msg.body}`;
            })
            .join('\n');
    }

    // Get function definitions for OpenAI
    getFunctionDefinitions() {
        return [
            {
                name: "send_whatsapp_message",
                description: "Send a WhatsApp message to a specific contact",
                parameters: {
                    type: "object",
                    properties: {
                        to: {
                            type: "string",
                            description: "Phone number to send message to (with country code)"
                        },
                        message: {
                            type: "string",
                            description: "Message content to send"
                        }
                    },
                    required: ["to", "message"]
                }
            },
            {
                name: "schedule_message",
                description: "Schedule a WhatsApp message for later delivery",
                parameters: {
                    type: "object",
                    properties: {
                        to: {
                            type: "string",
                            description: "Phone number to send message to"
                        },
                        message: {
                            type: "string",
                            description: "Message content to send"
                        },
                        datetime: {
                            type: "string",
                            description: "When to send the message (ISO 8601 format)"
                        }
                    },
                    required: ["to", "message", "datetime"]
                }
            },
            {
                name: "search_messages",
                description: "Search through message history",
                parameters: {
                    type: "object",
                    properties: {
                        query: {
                            type: "string",
                            description: "Search query"
                        },
                        contact: {
                            type: "string",
                            description: "Optional: specific contact to search messages from"
                        },
                        limit: {
                            type: "number",
                            description: "Maximum number of results to return (default: 10)"
                        }
                    },
                    required: ["query"]
                }
            },
            {
                name: "get_message_stats",
                description: "Get statistics about messages with a contact",
                parameters: {
                    type: "object",
                    properties: {
                        contact: {
                            type: "string",
                            description: "Contact phone number"
                        },
                        days: {
                            type: "number",
                            description: "Number of days to analyze (default: 7)"
                        }
                    },
                    required: ["contact"]
                }
            },
            {
                name: "get_today_summary",
                description: "Get a summary of today's messages",
                parameters: {
                    type: "object",
                    properties: {
                        contact: {
                            type: "string",
                            description: "Optional: specific contact to get summary for"
                        }
                    }
                }
            }
        ];
    }

    // Process AI response and handle function calls
    async processResponse(response, userId, platform, context) {
        try {
            const choice = response.choices[0];
            const message = choice.message;

            let result = {
                text: null,
                functionCalls: [],
                replyToMessage: false
            };

            // Handle function calls
            if (message.function_call) {
                const functionCall = {
                    name: message.function_call.name,
                    arguments: JSON.parse(message.function_call.arguments)
                };

                const functionResult = await this.executeFunctionCall(
                    functionCall,
                    userId,
                    platform,
                    context
                );

                result.functionCalls.push({
                    ...functionCall,
                    result: functionResult
                });

                // Generate response based on function result
                result.text = await this.generateFunctionResponse(functionCall, functionResult);
            } else {
                // Regular text response
                result.text = message.content;
            }

            return result;

        } catch (error) {
            logger.error('Error processing AI response:', error);
            return {
                text: "I encountered an error processing your request. Please try again.",
                functionCalls: [],
                replyToMessage: false
            };
        }
    }

    // Execute function calls
    async executeFunctionCall(functionCall, userId, platform, context) {
        try {
            switch (functionCall.name) {
                case 'send_whatsapp_message':
                    return await this.executeSendMessage(functionCall.arguments);

                case 'schedule_message':
                    return await this.executeScheduleMessage(functionCall.arguments, userId);

                case 'search_messages':
                    return await this.executeSearchMessages(functionCall.arguments);

                case 'get_message_stats':
                    return await this.executeGetMessageStats(functionCall.arguments);

                case 'get_today_summary':
                    return await this.executeGetTodaySummary(functionCall.arguments);

                default:
                    logger.warn('Unknown function call:', functionCall.name);
                    return { error: 'Unknown function' };
            }
        } catch (error) {
            logger.error('Error executing function call:', error);
            return { error: error.message };
        }
    }

    // Execute send message function
    async executeSendMessage(args) {
        if (!this.whatsappClient) {
            return { error: 'WhatsApp client not available' };
        }

        try {
            const result = await this.whatsappClient.sendMessage(args.to, args.message);
            return { success: true, messageId: result.messageId };
        } catch (error) {
            return { error: error.message };
        }
    }

    // Execute schedule message function
    async executeScheduleMessage(args, userId) {
        try {
            const scheduledDate = new Date(args.datetime);
            
            if (scheduledDate <= new Date()) {
                return { error: 'Scheduled time must be in the future' };
            }

            const result = await this.dbService.saveScheduledMessage(
                args.to,
                args.message,
                scheduledDate,
                userId
            );

            return { 
                success: true, 
                scheduleId: result.id,
                scheduledFor: scheduledDate.toISOString()
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    // Execute search messages function
    async executeSearchMessages(args) {
        try {
            const options = {
                topK: args.limit || 10,
                minScore: 0.7
            };

            let results;
            if (args.contact) {
                results = await this.vectorService.searchMessagesByContact(
                    args.query,
                    args.contact,
                    options
                );
            } else {
                results = await this.vectorService.searchSimilarMessages(
                    args.query,
                    options
                );
            }

            return {
                success: true,
                results: results.map(r => ({
                    from: r.contactName || r.from,
                    message: r.body,
                    timestamp: new Date(r.timestamp).toLocaleString(),
                    score: r.score
                }))
            };
        } catch (error) {
            return { error: error.message };
        }
    }

    // Execute get message stats function
    async executeGetMessageStats(args) {
        try {
            const messages = await this.dbService.getMessageHistory(
                args.contact,
                1000
            );

            const days = args.days || 7;
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);

            const recentMessages = messages.filter(msg => 
                new Date(msg.created_at) >= cutoffDate
            );

            const stats = {
                totalMessages: messages.length,
                recentMessages: recentMessages.length,
                lastMessageTime: messages[0]?.created_at,
                messagesByDay: this.groupMessagesByDay(recentMessages)
            };

            return { success: true, stats };
        } catch (error) {
            return { error: error.message };
        }
    }

    // Execute get today summary function
    async executeGetTodaySummary(args) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            let messages;
            if (args.contact) {
                messages = await this.dbService.getTodayMessages(args.contact);
            } else {
                // Get all today's messages
                const result = await this.dbService.pool.query(`
                    SELECT * FROM messages 
                    WHERE timestamp >= $1
                    ORDER BY timestamp DESC
                    LIMIT 100
                `, [today.getTime()]);
                messages = result.rows;
            }

            const summary = {
                totalMessages: messages.length,
                contacts: [...new Set(messages.map(m => m.from_number))].length,
                lastActivity: messages[0]?.created_at
            };

            return { success: true, summary, messages: messages.slice(0, 10) };
        } catch (error) {
            return { error: error.message };
        }
    }

    // Generate response based on function result
    async generateFunctionResponse(functionCall, result) {
        if (result.error) {
            return `❌ Error: ${result.error}`;
        }

        switch (functionCall.name) {
            case 'send_whatsapp_message':
                return `✅ Message sent successfully to ${functionCall.arguments.to}`;

            case 'schedule_message':
                return `✅ Message scheduled for ${result.scheduledFor}`;

            case 'search_messages':
                if (result.results.length === 0) {
                    return "No messages found matching your search.";
                }
                return `Found ${result.results.length} messages:\n\n` +
                    result.results.slice(0, 5).map(r => 
                        `📱 ${r.from} (${r.timestamp}): ${r.message.substring(0, 100)}...`
                    ).join('\n');

            case 'get_message_stats':
                return `📊 Message Statistics:\n` +
                    `• Total messages: ${result.stats.totalMessages}\n` +
                    `• Recent messages: ${result.stats.recentMessages}\n` +
                    `• Last message: ${result.stats.lastMessageTime}`;

            case 'get_today_summary':
                return `📅 Today's Summary:\n` +
                    `• Total messages: ${result.summary.totalMessages}\n` +
                    `• Active contacts: ${result.summary.contacts}\n` +
                    `• Last activity: ${result.summary.lastActivity}`;

            default:
                return "Function executed successfully.";
        }
    }

    // Store message embedding
    async storeMessageEmbedding(messageData) {
        try {
            await this.vectorService.storeMessage(messageData);
        } catch (error) {
            logger.error('Error storing message embedding:', error);
        }
    }

    // Utility function to group messages by day
    groupMessagesByDay(messages) {
        const groups = {};
        messages.forEach(msg => {
            const day = new Date(msg.created_at).toDateString();
            groups[day] = (groups[day] || 0) + 1;
        });
        return groups;
    }

    // Set WhatsApp client
    setWhatsAppClient(whatsappClient) {
        this.whatsappClient = whatsappClient;
    }

    // Set Telegram bot
    setTelegramBot(telegramBot) {
        this.telegramBot = telegramBot;
    }

    // Health check
    async healthCheck() {
        try {
            // Test OpenAI connection
            await this.openai.models.list();
            
            // Test vector service
            const vectorHealth = await this.vectorService.healthCheck();
            
            return {
                status: 'healthy',
                model: this.model,
                vectorService: vectorHealth.status
            };
        } catch (error) {
            logger.error('AI Assistant health check failed:', error);
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }
}

module.exports = AIAssistant;
