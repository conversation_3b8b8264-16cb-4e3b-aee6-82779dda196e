const cron = require('node-cron');
const DatabaseService = require('../database/dbService');
const logger = require('../../utils/logger');

class SchedulerService {
    constructor() {
        this.dbService = new DatabaseService();
        this.whatsappClient = null;
        this.telegramBot = null;
        this.scheduledJobs = new Map();
        this.isRunning = false;
        this.checkInterval = null;
        this.enabled = process.env.SCHEDULER_ENABLED !== 'false';
    }

    async initialize() {
        try {
            await this.dbService.initialize();
            
            if (this.enabled) {
                await this.startScheduler();
                logger.info('Scheduler service initialized successfully');
            } else {
                logger.info('Scheduler service disabled');
            }
        } catch (error) {
            logger.error('Failed to initialize scheduler service:', error);
            throw error;
        }
    }

    // Start the scheduler
    async startScheduler() {
        if (this.isRunning) {
            logger.warn('Scheduler is already running');
            return;
        }

        try {
            // Check for pending messages every minute
            this.checkInterval = cron.schedule('* * * * *', async () => {
                await this.processPendingMessages();
            }, {
                scheduled: true,
                timezone: "UTC"
            });

            // Cleanup old completed messages daily at 2 AM
            cron.schedule('0 2 * * *', async () => {
                await this.cleanupOldMessages();
            }, {
                scheduled: true,
                timezone: "UTC"
            });

            // Health check every 5 minutes
            cron.schedule('*/5 * * * *', async () => {
                await this.healthCheck();
            }, {
                scheduled: true,
                timezone: "UTC"
            });

            this.isRunning = true;
            logger.info('Scheduler started successfully');

        } catch (error) {
            logger.error('Error starting scheduler:', error);
            throw error;
        }
    }

    // Stop the scheduler
    async stopScheduler() {
        try {
            if (this.checkInterval) {
                this.checkInterval.stop();
                this.checkInterval = null;
            }

            // Stop all individual scheduled jobs
            for (const [jobId, job] of this.scheduledJobs) {
                if (job.stop) {
                    job.stop();
                }
            }
            this.scheduledJobs.clear();

            this.isRunning = false;
            logger.info('Scheduler stopped successfully');

        } catch (error) {
            logger.error('Error stopping scheduler:', error);
        }
    }

    // Process pending scheduled messages
    async processPendingMessages() {
        try {
            const pendingMessages = await this.dbService.getPendingScheduledMessages();
            
            if (pendingMessages.length === 0) {
                return;
            }

            logger.info(`Processing ${pendingMessages.length} pending messages`);

            for (const message of pendingMessages) {
                await this.executeScheduledMessage(message);
            }

        } catch (error) {
            logger.error('Error processing pending messages:', error);
        }
    }

    // Execute a scheduled message
    async executeScheduledMessage(scheduledMessage) {
        try {
            logger.info('Executing scheduled message', {
                id: scheduledMessage.id,
                to: scheduledMessage.to_number,
                scheduledFor: scheduledMessage.scheduled_for
            });

            let success = false;
            let errorMessage = null;

            // Try to send the message
            if (this.whatsappClient && this.whatsappClient.isReady) {
                try {
                    const result = await this.whatsappClient.sendMessage(
                        scheduledMessage.to_number,
                        scheduledMessage.message
                    );
                    
                    if (result.success) {
                        success = true;
                        logger.info('Scheduled message sent successfully', {
                            id: scheduledMessage.id,
                            messageId: result.messageId
                        });
                    } else {
                        errorMessage = 'Failed to send message';
                    }
                } catch (error) {
                    errorMessage = error.message;
                    logger.error('Error sending scheduled message:', error);
                }
            } else {
                errorMessage = 'WhatsApp client not ready';
                logger.warn('WhatsApp client not ready for scheduled message', {
                    id: scheduledMessage.id
                });
            }

            // Update message status
            const status = success ? 'completed' : 'failed';
            await this.dbService.updateScheduledMessageStatus(
                scheduledMessage.id,
                status,
                errorMessage,
                new Date()
            );

            // Notify via Telegram if configured
            if (this.telegramBot && success) {
                await this.telegramBot.sendNotification(
                    `✅ Scheduled message sent to ${scheduledMessage.to_number}\n\n` +
                    `Message: ${scheduledMessage.message.substring(0, 100)}...`
                );
            } else if (this.telegramBot && !success) {
                await this.telegramBot.sendNotification(
                    `❌ Failed to send scheduled message to ${scheduledMessage.to_number}\n\n` +
                    `Error: ${errorMessage}\n` +
                    `Message: ${scheduledMessage.message.substring(0, 100)}...`
                );
            }

        } catch (error) {
            logger.error('Error executing scheduled message:', error);
            
            // Update status to failed
            try {
                await this.dbService.updateScheduledMessageStatus(
                    scheduledMessage.id,
                    'failed',
                    error.message,
                    new Date()
                );
            } catch (updateError) {
                logger.error('Error updating failed message status:', updateError);
            }
        }
    }

    // Schedule a message with cron
    async scheduleMessageWithCron(toNumber, message, scheduledDate, createdBy = null) {
        try {
            // Save to database first
            const savedMessage = await this.dbService.saveScheduledMessage(
                toNumber,
                message,
                scheduledDate,
                createdBy
            );

            // Create cron expression for the specific date/time
            const cronExpression = this.dateToCron(scheduledDate);
            
            // Schedule with node-cron
            const job = cron.schedule(cronExpression, async () => {
                await this.executeScheduledMessage(savedMessage);
                
                // Remove from active jobs after execution
                this.scheduledJobs.delete(savedMessage.id);
                job.stop();
            }, {
                scheduled: false,
                timezone: "UTC"
            });

            // Start the job
            job.start();
            
            // Store reference to the job
            this.scheduledJobs.set(savedMessage.id, job);

            logger.info('Message scheduled with cron', {
                id: savedMessage.id,
                scheduledFor: scheduledDate.toISOString(),
                cronExpression
            });

            return {
                success: true,
                scheduleId: savedMessage.id,
                scheduledFor: scheduledDate.toISOString(),
                cronExpression
            };

        } catch (error) {
            logger.error('Error scheduling message with cron:', error);
            throw error;
        }
    }

    // Cancel a scheduled message
    async cancelScheduledMessage(scheduleId) {
        try {
            // Update database status
            await this.dbService.updateScheduledMessageStatus(
                scheduleId,
                'cancelled',
                'Cancelled by user',
                new Date()
            );

            // Stop cron job if it exists
            if (this.scheduledJobs.has(scheduleId)) {
                const job = this.scheduledJobs.get(scheduleId);
                if (job.stop) {
                    job.stop();
                }
                this.scheduledJobs.delete(scheduleId);
            }

            logger.info('Scheduled message cancelled', { scheduleId });

            return { success: true, scheduleId };

        } catch (error) {
            logger.error('Error cancelling scheduled message:', error);
            throw error;
        }
    }

    // Get scheduled messages for a user
    async getScheduledMessages(createdBy = null, status = null) {
        try {
            let query = 'SELECT * FROM scheduled_messages WHERE 1=1';
            const params = [];
            let paramIndex = 1;

            if (createdBy) {
                query += ` AND created_by = $${paramIndex}`;
                params.push(createdBy);
                paramIndex++;
            }

            if (status) {
                query += ` AND status = $${paramIndex}`;
                params.push(status);
                paramIndex++;
            }

            query += ' ORDER BY scheduled_for ASC';

            const result = await this.dbService.pool.query(query, params);
            return result.rows;

        } catch (error) {
            logger.error('Error getting scheduled messages:', error);
            throw error;
        }
    }

    // Convert Date to cron expression
    dateToCron(date) {
        const minute = date.getUTCMinutes();
        const hour = date.getUTCHours();
        const day = date.getUTCDate();
        const month = date.getUTCMonth() + 1;
        const year = date.getUTCFullYear();

        // For one-time execution: minute hour day month *
        return `${minute} ${hour} ${day} ${month} *`;
    }

    // Parse cron expression to human readable format
    cronToHuman(cronExpression) {
        try {
            const parts = cronExpression.split(' ');
            if (parts.length >= 5) {
                const [minute, hour, day, month] = parts;
                return `${month}/${day} at ${hour.padStart(2, '0')}:${minute.padStart(2, '0')} UTC`;
            }
            return cronExpression;
        } catch (error) {
            return cronExpression;
        }
    }

    // Cleanup old completed/failed messages
    async cleanupOldMessages(daysToKeep = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            const result = await this.dbService.pool.query(`
                DELETE FROM scheduled_messages 
                WHERE status IN ('completed', 'failed', 'cancelled')
                AND updated_at < $1
                RETURNING id
            `, [cutoffDate]);

            // Remove any corresponding cron jobs
            for (const row of result.rows) {
                if (this.scheduledJobs.has(row.id)) {
                    const job = this.scheduledJobs.get(row.id);
                    if (job.stop) {
                        job.stop();
                    }
                    this.scheduledJobs.delete(row.id);
                }
            }

            logger.info('Old scheduled messages cleaned up', {
                deletedCount: result.rowCount,
                cutoffDate: cutoffDate.toISOString()
            });

            return result.rowCount;

        } catch (error) {
            logger.error('Error cleaning up old messages:', error);
            return 0;
        }
    }

    // Get scheduler statistics
    async getSchedulerStats() {
        try {
            const stats = await this.dbService.pool.query(`
                SELECT 
                    status,
                    COUNT(*) as count
                FROM scheduled_messages 
                GROUP BY status
            `);

            const statusCounts = {};
            stats.rows.forEach(row => {
                statusCounts[row.status] = parseInt(row.count);
            });

            return {
                isRunning: this.isRunning,
                enabled: this.enabled,
                activeJobs: this.scheduledJobs.size,
                statusCounts,
                totalScheduled: Object.values(statusCounts).reduce((a, b) => a + b, 0)
            };

        } catch (error) {
            logger.error('Error getting scheduler stats:', error);
            return null;
        }
    }

    // Set WhatsApp client
    setWhatsAppClient(whatsappClient) {
        this.whatsappClient = whatsappClient;
    }

    // Set Telegram bot
    setTelegramBot(telegramBot) {
        this.telegramBot = telegramBot;
    }

    // Health check
    async healthCheck() {
        try {
            const stats = await this.getSchedulerStats();
            const dbHealth = await this.dbService.healthCheck();

            const health = {
                status: this.isRunning && dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
                isRunning: this.isRunning,
                enabled: this.enabled,
                activeJobs: this.scheduledJobs.size,
                database: dbHealth.status,
                stats
            };

            if (health.status === 'unhealthy') {
                logger.warn('Scheduler health check failed', health);
            }

            return health;

        } catch (error) {
            logger.error('Scheduler health check error:', error);
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }
}

module.exports = SchedulerService;
