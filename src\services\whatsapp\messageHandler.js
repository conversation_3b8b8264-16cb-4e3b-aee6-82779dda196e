const DatabaseService = require('../database/dbService');
const logger = require('../../utils/logger');

class WhatsAppMessageHandler {
    constructor(whatsappClient) {
        this.whatsappClient = whatsappClient;
        this.dbService = new DatabaseService();
        this.aiAssistant = null;
        this.initialize();
    }

    async initialize() {
        await this.dbService.initialize();
    }

    // Handle incoming messages
    async handleIncomingMessage(message) {
        try {
            // Skip status messages and other non-text messages we don't want to process
            if (message.isStatus || message.type === 'notification_template') {
                return;
            }

            const messageData = await this.extractMessageData(message);
            
            // Save message to database
            await this.saveMessage(messageData);
            
            // Process with AI if it's a direct message or mentions us
            if (await this.shouldProcessWithAI(message, messageData)) {
                await this.processWithAI(messageData, message);
            }

            logger.info('Incoming message processed', {
                messageId: messageData.id,
                from: messageData.from,
                type: messageData.type
            });

        } catch (error) {
            logger.error('Error handling incoming message:', error);
        }
    }

    // Handle outgoing messages (sent by us)
    async handleOutgoingMessage(message) {
        try {
            const messageData = await this.extractMessageData(message);
            
            // Save our own messages to maintain conversation context
            await this.saveMessage(messageData);

            logger.info('Outgoing message processed', {
                messageId: messageData.id,
                to: messageData.to,
                type: messageData.type
            });

        } catch (error) {
            logger.error('Error handling outgoing message:', error);
        }
    }

    // Extract message data from WhatsApp message object
    async extractMessageData(message) {
        try {
            const contact = await message.getContact();
            const chat = await message.getChat();
            
            const messageData = {
                id: message.id._serialized,
                from: message.from,
                to: message.to,
                body: message.body,
                timestamp: message.timestamp,
                type: message.type,
                isGroup: message.from.includes('@g.us'),
                isFromMe: message.fromMe,
                contact: {
                    name: contact.name || contact.pushname || contact.number,
                    number: contact.number,
                    isBusiness: contact.isBusiness,
                    isEnterprise: contact.isEnterprise
                },
                chat: {
                    id: chat.id._serialized,
                    name: chat.name,
                    isGroup: chat.isGroup,
                    isReadOnly: chat.isReadOnly,
                    unreadCount: chat.unreadCount
                },
                hasMedia: message.hasMedia,
                isForwarded: message.isForwarded,
                replyToId: message._data.quotedMsg?.id || null
            };

            // Handle media messages
            if (message.hasMedia) {
                try {
                    const media = await message.downloadMedia();
                    messageData.media = {
                        mimetype: media.mimetype,
                        data: media.data,
                        filename: media.filename
                    };
                } catch (error) {
                    logger.error('Error downloading media:', error);
                }
            }

            return messageData;
        } catch (error) {
            logger.error('Error extracting message data:', error);
            throw error;
        }
    }

    // Save message to database and vector store
    async saveMessage(messageData) {
        try {
            // Save to relational database
            await this.dbService.saveMessage(messageData);
            
            // Save contact information
            if (messageData.contact) {
                await this.dbService.saveContact({
                    number: messageData.contact.number,
                    name: messageData.contact.name,
                    isBusiness: messageData.contact.isBusiness,
                    isEnterprise: messageData.contact.isEnterprise
                });
            }

            // Create embedding and save to vector database if AI assistant is available
            if (this.aiAssistant && messageData.body && messageData.body.trim().length > 0) {
                await this.aiAssistant.storeMessageEmbedding(messageData);
            }

        } catch (error) {
            logger.error('Error saving message:', error);
            throw error;
        }
    }

    // Determine if message should be processed by AI
    async shouldProcessWithAI(message, messageData) {
        try {
            // Don't process our own messages
            if (messageData.isFromMe) {
                return false;
            }

            // Don't process system messages
            if (message.type === 'notification_template' || message.isStatus) {
                return false;
            }

            // Process direct messages (not in groups)
            if (!messageData.isGroup) {
                return true;
            }

            // In groups, only process if we're mentioned or it's a command
            if (messageData.isGroup) {
                const body = messageData.body.toLowerCase();
                
                // Check for mentions (this would need to be customized based on your bot name)
                const botMentions = ['@assistant', '@ai', '@bot'];
                const isMentioned = botMentions.some(mention => body.includes(mention));
                
                // Check for commands
                const isCommand = body.startsWith('/') || body.startsWith('!');
                
                return isMentioned || isCommand;
            }

            return false;
        } catch (error) {
            logger.error('Error determining if should process with AI:', error);
            return false;
        }
    }

    // Process message with AI
    async processWithAI(messageData, originalMessage) {
        try {
            if (!this.aiAssistant) {
                logger.warn('AI Assistant not available');
                return;
            }

            // Get AI response
            const response = await this.aiAssistant.processMessage(
                messageData.body,
                messageData.from,
                'whatsapp',
                {
                    messageData,
                    isGroup: messageData.isGroup,
                    contact: messageData.contact,
                    chat: messageData.chat
                }
            );

            // Send response if AI generated one
            if (response && response.text) {
                if (response.replyToMessage) {
                    // Reply to the original message
                    await this.whatsappClient.replyToMessage(originalMessage, response.text);
                } else {
                    // Send as new message
                    await this.whatsappClient.sendMessage(messageData.from, response.text);
                }
            }

            // Execute any function calls
            if (response && response.functionCalls) {
                await this.executeFunctionCalls(response.functionCalls, messageData);
            }

        } catch (error) {
            logger.error('Error processing message with AI:', error);
            
            // Send error message to user in development mode
            if (process.env.NODE_ENV === 'development') {
                try {
                    await this.whatsappClient.sendMessage(
                        messageData.from,
                        'Sorry, I encountered an error processing your message. Please try again.'
                    );
                } catch (sendError) {
                    logger.error('Error sending error message:', sendError);
                }
            }
        }
    }

    // Execute function calls from AI
    async executeFunctionCalls(functionCalls, messageData) {
        try {
            for (const functionCall of functionCalls) {
                switch (functionCall.name) {
                    case 'send_whatsapp_message':
                        await this.whatsappClient.sendMessage(
                            functionCall.arguments.to,
                            functionCall.arguments.message
                        );
                        break;
                        
                    case 'schedule_message':
                        await this.dbService.saveScheduledMessage(
                            functionCall.arguments.to,
                            functionCall.arguments.message,
                            new Date(functionCall.arguments.datetime),
                            messageData.from
                        );
                        break;
                        
                    case 'search_messages':
                        // This would be handled by returning search results to the AI
                        break;
                        
                    default:
                        logger.warn('Unknown function call:', functionCall.name);
                }
            }
        } catch (error) {
            logger.error('Error executing function calls:', error);
        }
    }

    // Set AI Assistant
    setAIAssistant(aiAssistant) {
        this.aiAssistant = aiAssistant;
    }

    // Get message statistics
    async getMessageStats(contactNumber, days = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            
            const messages = await this.dbService.getMessageHistory(contactNumber, 1000);
            const recentMessages = messages.filter(msg => 
                new Date(msg.created_at) >= cutoffDate
            );
            
            return {
                totalMessages: messages.length,
                recentMessages: recentMessages.length,
                lastMessageTime: messages[0]?.created_at,
                messageTypes: this.groupBy(recentMessages, 'message_type')
            };
        } catch (error) {
            logger.error('Error getting message stats:', error);
            return null;
        }
    }

    // Utility function to group array by property
    groupBy(array, property) {
        return array.reduce((groups, item) => {
            const key = item[property];
            groups[key] = groups[key] || [];
            groups[key].push(item);
            return groups;
        }, {});
    }
}

module.exports = WhatsAppMessageHandler;
