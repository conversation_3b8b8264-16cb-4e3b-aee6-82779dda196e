const AIAssistant = require('../../src/services/ai/assistant');
const VectorService = require('../../src/services/database/vectorService');
const ContextService = require('../../src/services/ai/contextService');
const DatabaseService = require('../../src/services/database/dbService');

// Mock dependencies
jest.mock('../../src/services/database/vectorService');
jest.mock('../../src/services/ai/contextService');
jest.mock('../../src/services/database/dbService');
jest.mock('openai');

describe('AI Assistant', () => {
    let aiAssistant;
    let mockVectorService;
    let mockContextService;
    let mockDbService;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        
        // Create mock instances
        mockVectorService = new VectorService();
        mockContextService = new ContextService();
        mockDbService = new DatabaseService();
        
        // Mock methods
        mockVectorService.initialize = jest.fn().mockResolvedValue();
        mockVectorService.searchSimilarMessages = jest.fn().mockResolvedValue([]);
        mockVectorService.storeMessage = jest.fn().mockResolvedValue('vector-id');
        
        mockContextService.initialize = jest.fn().mockResolvedValue();
        mockContextService.getConversationContext = jest.fn().mockResolvedValue([]);
        mockContextService.addToConversationContext = jest.fn().mockResolvedValue();
        
        mockDbService.initialize = jest.fn().mockResolvedValue();
        mockDbService.saveAIConversation = jest.fn().mockResolvedValue();
        
        aiAssistant = new AIAssistant();
        aiAssistant.vectorService = mockVectorService;
        aiAssistant.contextService = mockContextService;
        aiAssistant.dbService = mockDbService;
    });

    describe('initialization', () => {
        test('should initialize all services', async () => {
            // Mock OpenAI
            const mockOpenAI = {
                chat: {
                    completions: {
                        create: jest.fn()
                    }
                }
            };
            
            require('openai').mockImplementation(() => mockOpenAI);
            
            await aiAssistant.initialize();
            
            expect(mockVectorService.initialize).toHaveBeenCalled();
            expect(mockContextService.initialize).toHaveBeenCalled();
            expect(mockDbService.initialize).toHaveBeenCalled();
        });

        test('should throw error if initialization fails', async () => {
            mockVectorService.initialize.mockRejectedValue(new Error('Vector service failed'));
            
            await expect(aiAssistant.initialize()).rejects.toThrow('Vector service failed');
        });
    });

    describe('message processing', () => {
        beforeEach(async () => {
            // Mock OpenAI response
            const mockOpenAI = {
                chat: {
                    completions: {
                        create: jest.fn().mockResolvedValue({
                            choices: [{
                                message: {
                                    content: 'Test response',
                                    function_call: null
                                }
                            }]
                        })
                    }
                }
            };
            
            require('openai').mockImplementation(() => mockOpenAI);
            await aiAssistant.initialize();
        });

        test('should process message with context', async () => {
            const message = 'Hello, how are you?';
            const userId = 'user123';
            const platform = 'whatsapp';
            
            const response = await aiAssistant.processMessage(message, userId, platform);
            
            expect(response).toHaveProperty('text');
            expect(response.text).toBe('Test response');
            expect(mockContextService.getConversationContext).toHaveBeenCalledWith(userId, platform);
            expect(mockVectorService.searchSimilarMessages).toHaveBeenCalledWith(message, { topK: 5, minScore: 0.75 });
        });

        test('should handle function calls', async () => {
            const mockOpenAI = aiAssistant.openai;
            mockOpenAI.chat.completions.create.mockResolvedValue({
                choices: [{
                    message: {
                        content: null,
                        function_call: {
                            name: 'send_whatsapp_message',
                            arguments: JSON.stringify({
                                to: '+1234567890',
                                message: 'Test message'
                            })
                        }
                    }
                }]
            });

            // Mock WhatsApp client
            const mockWhatsAppClient = {
                sendMessage: jest.fn().mockResolvedValue({ success: true, messageId: 'msg123' })
            };
            aiAssistant.setWhatsAppClient(mockWhatsAppClient);

            const response = await aiAssistant.processMessage('Send a message', 'user123', 'whatsapp');
            
            expect(response.functionCalls).toHaveLength(1);
            expect(response.functionCalls[0].name).toBe('send_whatsapp_message');
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledWith('+1234567890', 'Test message');
        });

        test('should handle errors gracefully', async () => {
            mockVectorService.searchSimilarMessages.mockRejectedValue(new Error('Vector search failed'));
            
            await expect(aiAssistant.processMessage('test', 'user123', 'whatsapp')).rejects.toThrow();
        });
    });

    describe('function execution', () => {
        beforeEach(async () => {
            const mockOpenAI = {
                chat: { completions: { create: jest.fn() } }
            };
            require('openai').mockImplementation(() => mockOpenAI);
            await aiAssistant.initialize();
        });

        test('should execute send message function', async () => {
            const mockWhatsAppClient = {
                sendMessage: jest.fn().mockResolvedValue({ success: true, messageId: 'msg123' })
            };
            aiAssistant.setWhatsAppClient(mockWhatsAppClient);

            const functionCall = {
                name: 'send_whatsapp_message',
                arguments: { to: '+1234567890', message: 'Test message' }
            };

            const result = await aiAssistant.executeFunctionCall(functionCall, 'user123', 'whatsapp');
            
            expect(result.success).toBe(true);
            expect(result.messageId).toBe('msg123');
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledWith('+1234567890', 'Test message');
        });

        test('should execute schedule message function', async () => {
            mockDbService.saveScheduledMessage = jest.fn().mockResolvedValue({
                id: 'schedule123',
                scheduled_for: new Date('2024-01-15T14:30:00Z')
            });

            const functionCall = {
                name: 'schedule_message',
                arguments: {
                    to: '+1234567890',
                    message: 'Scheduled message',
                    datetime: '2024-01-15T14:30:00Z'
                }
            };

            const result = await aiAssistant.executeFunctionCall(functionCall, 'user123', 'whatsapp');
            
            expect(result.success).toBe(true);
            expect(result.scheduleId).toBe('schedule123');
            expect(mockDbService.saveScheduledMessage).toHaveBeenCalled();
        });

        test('should execute search messages function', async () => {
            const mockSearchResults = [
                {
                    messageId: 'msg1',
                    contactName: 'John',
                    from: '+1234567890',
                    body: 'Hello world',
                    timestamp: Date.now(),
                    score: 0.9
                }
            ];
            
            mockVectorService.searchSimilarMessages.mockResolvedValue(mockSearchResults);

            const functionCall = {
                name: 'search_messages',
                arguments: { query: 'hello', limit: 10 }
            };

            const result = await aiAssistant.executeFunctionCall(functionCall, 'user123', 'whatsapp');
            
            expect(result.success).toBe(true);
            expect(result.results).toHaveLength(1);
            expect(result.results[0].from).toBe('John');
        });

        test('should handle unknown function calls', async () => {
            const functionCall = {
                name: 'unknown_function',
                arguments: {}
            };

            const result = await aiAssistant.executeFunctionCall(functionCall, 'user123', 'whatsapp');
            
            expect(result.error).toBe('Unknown function');
        });
    });

    describe('embedding storage', () => {
        test('should store message embedding', async () => {
            const messageData = {
                id: 'msg123',
                body: 'Test message',
                from: '+1234567890',
                timestamp: Date.now()
            };

            await aiAssistant.storeMessageEmbedding(messageData);
            
            expect(mockVectorService.storeMessage).toHaveBeenCalledWith(messageData);
        });

        test('should handle embedding storage errors', async () => {
            mockVectorService.storeMessage.mockRejectedValue(new Error('Storage failed'));
            
            const messageData = {
                id: 'msg123',
                body: 'Test message'
            };

            // Should not throw, just log error
            await expect(aiAssistant.storeMessageEmbedding(messageData)).resolves.toBeUndefined();
        });
    });

    describe('health check', () => {
        test('should return healthy status', async () => {
            const mockOpenAI = {
                models: {
                    list: jest.fn().mockResolvedValue({ data: [] })
                }
            };
            require('openai').mockImplementation(() => mockOpenAI);
            await aiAssistant.initialize();

            mockVectorService.healthCheck = jest.fn().mockResolvedValue({ status: 'healthy' });

            const health = await aiAssistant.healthCheck();
            
            expect(health.status).toBe('healthy');
            expect(health.model).toBe(aiAssistant.model);
            expect(health.vectorService).toBe('healthy');
        });

        test('should return unhealthy status on error', async () => {
            const mockOpenAI = {
                models: {
                    list: jest.fn().mockRejectedValue(new Error('API error'))
                }
            };
            require('openai').mockImplementation(() => mockOpenAI);
            await aiAssistant.initialize();

            const health = await aiAssistant.healthCheck();
            
            expect(health.status).toBe('unhealthy');
            expect(health.error).toBe('API error');
        });
    });
});
