const { Pinecone } = require('@pinecone-database/pinecone');
const { OpenAIEmbeddings } = require('@langchain/openai');
const logger = require('../../utils/logger');
const DatabaseService = require('./dbService');

class VectorService {
    constructor() {
        this.pinecone = null;
        this.index = null;
        this.embeddings = null;
        this.dbService = new DatabaseService();
        this.embeddingModel = process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-ada-002';
        this.batchSize = parseInt(process.env.EMBEDDING_BATCH_SIZE) || 10;
    }

    async initialize() {
        try {
            // Initialize Pinecone
            this.pinecone = new Pinecone({
                apiKey: process.env.PINECONE_API_KEY,
            });

            // Get or create index
            const indexName = process.env.PINECONE_INDEX || 'whatsapp-messages';
            this.index = this.pinecone.index(indexName);

            // Initialize OpenAI embeddings
            this.embeddings = new OpenAIEmbeddings({
                openAIApiKey: process.env.OPENAI_API_KEY,
                modelName: this.embeddingModel,
                batchSize: this.batchSize
            });

            // Initialize database service
            await this.dbService.initialize();

            logger.info('Vector service initialized successfully', {
                indexName,
                embeddingModel: this.embeddingModel
            });

        } catch (error) {
            logger.error('Failed to initialize vector service:', error);
            throw error;
        }
    }

    // Store a single message embedding
    async storeMessage(messageData) {
        try {
            if (!messageData.body || messageData.body.trim().length === 0) {
                return null;
            }

            // Generate embedding
            const embedding = await this.embeddings.embedQuery(messageData.body);
            
            // Create unique vector ID
            const vectorId = `msg_${messageData.id}`;

            // Prepare metadata
            const metadata = {
                messageId: messageData.id,
                from: messageData.from,
                to: messageData.to || '',
                timestamp: messageData.timestamp,
                isGroup: messageData.isGroup || false,
                contactName: messageData.contact?.name || '',
                chatId: messageData.chat?.id || '',
                messageType: messageData.type || 'text',
                body: messageData.body.substring(0, 1000), // Limit body size for metadata
                createdAt: new Date().toISOString()
            };

            // Store in Pinecone
            await this.index.upsert([{
                id: vectorId,
                values: embedding,
                metadata: metadata
            }]);

            // Store embedding reference in database
            await this.dbService.saveMessageEmbedding(
                messageData.id,
                vectorId,
                this.embeddingModel
            );

            logger.debug('Message embedding stored', {
                messageId: messageData.id,
                vectorId,
                embeddingDimension: embedding.length
            });

            return vectorId;

        } catch (error) {
            logger.error('Error storing message embedding:', error);
            throw error;
        }
    }

    // Store multiple messages in batch
    async storeMessagesBatch(messagesData) {
        try {
            const validMessages = messagesData.filter(msg => 
                msg.body && msg.body.trim().length > 0
            );

            if (validMessages.length === 0) {
                return [];
            }

            // Generate embeddings for all messages
            const texts = validMessages.map(msg => msg.body);
            const embeddings = await this.embeddings.embedDocuments(texts);

            // Prepare vectors for Pinecone
            const vectors = validMessages.map((messageData, index) => {
                const vectorId = `msg_${messageData.id}`;
                
                const metadata = {
                    messageId: messageData.id,
                    from: messageData.from,
                    to: messageData.to || '',
                    timestamp: messageData.timestamp,
                    isGroup: messageData.isGroup || false,
                    contactName: messageData.contact?.name || '',
                    chatId: messageData.chat?.id || '',
                    messageType: messageData.type || 'text',
                    body: messageData.body.substring(0, 1000),
                    createdAt: new Date().toISOString()
                };

                return {
                    id: vectorId,
                    values: embeddings[index],
                    metadata: metadata
                };
            });

            // Store in Pinecone
            await this.index.upsert(vectors);

            // Store embedding references in database
            const embeddingPromises = validMessages.map((messageData, index) => 
                this.dbService.saveMessageEmbedding(
                    messageData.id,
                    `msg_${messageData.id}`,
                    this.embeddingModel
                )
            );

            await Promise.all(embeddingPromises);

            logger.info('Batch message embeddings stored', {
                count: validMessages.length,
                embeddingModel: this.embeddingModel
            });

            return vectors.map(v => v.id);

        } catch (error) {
            logger.error('Error storing message embeddings batch:', error);
            throw error;
        }
    }

    // Search for similar messages
    async searchSimilarMessages(query, options = {}) {
        try {
            const {
                topK = 10,
                filter = {},
                includeMetadata = true,
                minScore = 0.7
            } = options;

            // Generate query embedding
            const queryEmbedding = await this.embeddings.embedQuery(query);

            // Search in Pinecone
            const searchResults = await this.index.query({
                vector: queryEmbedding,
                topK: topK,
                filter: filter,
                includeMetadata: includeMetadata
            });

            // Filter by minimum score and format results
            const filteredResults = searchResults.matches
                .filter(match => match.score >= minScore)
                .map(match => ({
                    id: match.id,
                    score: match.score,
                    messageId: match.metadata?.messageId,
                    from: match.metadata?.from,
                    to: match.metadata?.to,
                    body: match.metadata?.body,
                    timestamp: match.metadata?.timestamp,
                    contactName: match.metadata?.contactName,
                    isGroup: match.metadata?.isGroup,
                    chatId: match.metadata?.chatId,
                    messageType: match.metadata?.messageType,
                    createdAt: match.metadata?.createdAt
                }));

            logger.debug('Vector search completed', {
                query: query.substring(0, 100),
                resultsCount: filteredResults.length,
                topScore: filteredResults[0]?.score
            });

            return filteredResults;

        } catch (error) {
            logger.error('Error searching similar messages:', error);
            throw error;
        }
    }

    // Search messages by contact
    async searchMessagesByContact(query, contactNumber, options = {}) {
        const filter = {
            $or: [
                { from: { $eq: contactNumber } },
                { to: { $eq: contactNumber } }
            ]
        };

        return await this.searchSimilarMessages(query, {
            ...options,
            filter
        });
    }

    // Search messages in specific chat
    async searchMessagesInChat(query, chatId, options = {}) {
        const filter = {
            chatId: { $eq: chatId }
        };

        return await this.searchSimilarMessages(query, {
            ...options,
            filter
        });
    }

    // Search messages by date range
    async searchMessagesByDateRange(query, startDate, endDate, options = {}) {
        const startTimestamp = new Date(startDate).getTime();
        const endTimestamp = new Date(endDate).getTime();

        const filter = {
            timestamp: {
                $gte: startTimestamp,
                $lte: endTimestamp
            }
        };

        return await this.searchSimilarMessages(query, {
            ...options,
            filter
        });
    }

    // Get message context (surrounding messages)
    async getMessageContext(messageId, contextSize = 5) {
        try {
            // Get the original message from database
            const originalMessage = await this.dbService.pool.query(
                'SELECT * FROM messages WHERE id = $1',
                [messageId]
            );

            if (originalMessage.rows.length === 0) {
                return null;
            }

            const message = originalMessage.rows[0];
            const timestamp = message.timestamp;
            const chatId = message.chat_id;

            // Get surrounding messages
            const contextMessages = await this.dbService.pool.query(`
                SELECT * FROM messages 
                WHERE chat_id = $1 
                AND timestamp BETWEEN $2 AND $3
                ORDER BY timestamp ASC
            `, [
                chatId,
                timestamp - (contextSize * 60000), // 5 minutes before
                timestamp + (contextSize * 60000)  // 5 minutes after
            ]);

            return {
                originalMessage: message,
                contextMessages: contextMessages.rows,
                totalContext: contextMessages.rows.length
            };

        } catch (error) {
            logger.error('Error getting message context:', error);
            throw error;
        }
    }

    // Delete message embedding
    async deleteMessageEmbedding(messageId) {
        try {
            const vectorId = `msg_${messageId}`;
            
            // Delete from Pinecone
            await this.index.deleteOne(vectorId);
            
            // Delete from database
            await this.dbService.pool.query(
                'DELETE FROM message_embeddings WHERE message_id = $1',
                [messageId]
            );

            logger.debug('Message embedding deleted', { messageId, vectorId });

        } catch (error) {
            logger.error('Error deleting message embedding:', error);
            throw error;
        }
    }

    // Get vector database statistics
    async getVectorStats() {
        try {
            const stats = await this.index.describeIndexStats();
            
            const dbStats = await this.dbService.pool.query(`
                SELECT 
                    COUNT(*) as total_embeddings,
                    COUNT(DISTINCT embedding_model) as unique_models,
                    MIN(created_at) as oldest_embedding,
                    MAX(created_at) as newest_embedding
                FROM message_embeddings
            `);

            return {
                pinecone: stats,
                database: dbStats.rows[0],
                embeddingModel: this.embeddingModel
            };

        } catch (error) {
            logger.error('Error getting vector stats:', error);
            throw error;
        }
    }

    // Cleanup old embeddings
    async cleanupOldEmbeddings(daysToKeep = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            // Get old embeddings from database
            const oldEmbeddings = await this.dbService.pool.query(`
                SELECT vector_id FROM message_embeddings 
                WHERE created_at < $1
            `, [cutoffDate]);

            if (oldEmbeddings.rows.length === 0) {
                return 0;
            }

            // Delete from Pinecone
            const vectorIds = oldEmbeddings.rows.map(row => row.vector_id);
            await this.index.deleteMany(vectorIds);

            // Delete from database
            const deleteResult = await this.dbService.pool.query(`
                DELETE FROM message_embeddings 
                WHERE created_at < $1
            `, [cutoffDate]);

            logger.info('Old embeddings cleaned up', {
                deletedCount: deleteResult.rowCount,
                cutoffDate: cutoffDate.toISOString()
            });

            return deleteResult.rowCount;

        } catch (error) {
            logger.error('Error cleaning up old embeddings:', error);
            throw error;
        }
    }

    // Health check
    async healthCheck() {
        try {
            // Test Pinecone connection
            const stats = await this.index.describeIndexStats();
            
            // Test embeddings
            const testEmbedding = await this.embeddings.embedQuery('test');
            
            return {
                status: 'healthy',
                pineconeStats: stats,
                embeddingDimension: testEmbedding.length,
                embeddingModel: this.embeddingModel
            };

        } catch (error) {
            logger.error('Vector service health check failed:', error);
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }
}

module.exports = VectorService;
